"""
OCR Conversion Service

This module provides functionality to convert OCR PDFs to non-OCR PDFs and manage
the conversion process including database and vector database updates.
"""

import os
import logging
from datetime import datetime
from werkzeug.utils import secure_filename
from app.services.pdf_processor import convert_ocr_to_non_ocr_pdf, detect_ocr_pdf
from app.services.chroma_manager import get_unified_chroma_db
from app.utils.content_db import get_db_connection, get_pdf_by_original_filename
from scripts.setup.create_temp_dirs import create_pdf_directory_structure

logger = logging.getLogger(__name__)


class OCRConversionService:
    """Service for handling OCR to non-OCR PDF conversions."""
    
    def __init__(self):
        self.logger = logger
    
    def convert_pdf_ocr_to_non_ocr(self, pdf_path, category, dpi=300, keep_only_non_ocr=False):
        """
        Convert an OCR PDF to non-OCR format.
        
        Args:
            pdf_path (str): Path to the OCR PDF file
            category (str): Category of the PDF
            dpi (int): DPI for conversion (default: 300)
            keep_only_non_ocr (bool): Whether to delete the original OCR file
            
        Returns:
            tuple: (success, message, conversion_info)
        """
        try:
            if not os.path.exists(pdf_path):
                return False, f"PDF file not found: {pdf_path}", {}
            
            # Detect if the PDF actually has OCR content
            self.logger.info(f"Detecting OCR content in: {pdf_path}")
            ocr_detection = detect_ocr_pdf(pdf_path)
            self.logger.info(f"OCR detection results: {ocr_detection}")
            
            if not ocr_detection.get('is_ocr_pdf', False):
                return False, "PDF does not appear to contain OCR text layers", {
                    'ocr_detection': ocr_detection
                }
            
            # Generate output filename
            pdf_filename = os.path.basename(pdf_path)
            pdf_dir = os.path.dirname(pdf_path)
            non_ocr_filename = f"non_ocr_{pdf_filename}"
            non_ocr_path = os.path.join(pdf_dir, non_ocr_filename)
            
            self.logger.info(f"Converting OCR PDF to non-OCR: {pdf_path} -> {non_ocr_path}")
            
            # Perform the conversion
            success, message, metadata = convert_ocr_to_non_ocr_pdf(pdf_path, non_ocr_path, dpi=dpi)
            
            if not success:
                return False, f"OCR conversion failed: {message}", {
                    'ocr_detection': ocr_detection
                }
            
            conversion_info = {
                'original_path': pdf_path,
                'converted_path': non_ocr_path,
                'original_filename': pdf_filename,
                'converted_filename': non_ocr_filename,
                'ocr_detection': ocr_detection,
                'conversion_metadata': metadata,
                'conversion_dpi': dpi,
                'keep_only_non_ocr': keep_only_non_ocr
            }
            
            self.logger.info(f"Successfully created non-OCR version: {non_ocr_filename}")
            return True, f"Successfully converted to non-OCR format: {non_ocr_filename}", conversion_info
            
        except Exception as e:
            error_message = f"Error during OCR conversion: {str(e)}"
            self.logger.error(error_message)
            return False, error_message, {}
    
    def update_database_after_conversion(self, conversion_info, category, original_filename=None):
        """
        Update database records after OCR conversion.
        
        Args:
            conversion_info (dict): Information from the conversion process
            category (str): Category of the PDF
            original_filename (str): Original filename for database lookup
            
        Returns:
            tuple: (success, message)
        """
        try:
            if not original_filename:
                original_filename = conversion_info.get('original_filename')
            
            if not original_filename:
                return False, "Original filename required for database update"
            
            # Get the existing PDF record
            existing_pdf = get_pdf_by_original_filename(original_filename, category)
            if not existing_pdf:
                return False, f"No existing PDF record found for {original_filename} in category {category}"
            
            # Update the database record
            with get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Update the record to indicate it has a non-OCR version
                cursor.execute('''
                    UPDATE pdf_documents 
                    SET has_non_ocr_version = ?, 
                        conversion_settings = ?,
                        updated_at = datetime('now')
                    WHERE id = ?
                ''', (
                    True,
                    str(conversion_info.get('conversion_metadata', {})),
                    existing_pdf['id']
                ))
                
                conn.commit()
                self.logger.info(f"Updated database record for {original_filename} with non-OCR conversion info")
            
            return True, "Database updated successfully"
            
        except Exception as e:
            error_message = f"Error updating database after conversion: {str(e)}"
            self.logger.error(error_message)
            return False, error_message
    
    def update_vector_database_after_conversion(self, conversion_info, category, original_filename=None):
        """
        Update vector database entries after OCR conversion.
        
        Args:
            conversion_info (dict): Information from the conversion process
            category (str): Category of the PDF
            original_filename (str): Original filename for vector database lookup
            
        Returns:
            tuple: (success, message)
        """
        try:
            if not original_filename:
                original_filename = conversion_info.get('original_filename')
            
            if not original_filename:
                return False, "Original filename required for vector database update"
            
            converted_filename = conversion_info.get('converted_filename')
            if not converted_filename:
                return False, "Converted filename required for vector database update"
            
            # Get the unified ChromaDB instance
            db = get_unified_chroma_db()
            
            # Search for documents with the original filename
            try:
                # Use similarity search with metadata filter to find documents
                all_docs = db.similarity_search(
                    "", 
                    k=1000,  # Large number to get all documents
                    filter={"source": original_filename, "category": category}
                )
                
                if all_docs:
                    self.logger.info(f"Found {len(all_docs)} vector entries to update from {original_filename} to {converted_filename}")
                    
                    # Delete old entries
                    try:
                        db.delete(where={"source": original_filename, "category": category})
                        self.logger.info(f"Deleted old vector entries for {original_filename}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete old vector entries: {str(e)}")
                    
                    # Update document metadata and re-add
                    updated_docs = []
                    for doc in all_docs:
                        doc.metadata["source"] = converted_filename
                        updated_docs.append(doc)
                    
                    if updated_docs:
                        db.add_documents(updated_docs)
                        self.logger.info(f"Re-added {len(updated_docs)} vector entries with updated filename {converted_filename}")
                else:
                    self.logger.warning(f"No vector entries found for {original_filename} in category {category}")
                
                return True, f"Vector database updated successfully for {len(all_docs)} documents"
                
            except Exception as e:
                error_message = f"Error updating vector database entries: {str(e)}"
                self.logger.error(error_message)
                return False, error_message
            
        except Exception as e:
            error_message = f"Error in vector database update: {str(e)}"
            self.logger.error(error_message)
            return False, error_message
    
    def cleanup_original_file(self, conversion_info):
        """
        Clean up the original OCR file if requested.
        
        Args:
            conversion_info (dict): Information from the conversion process
            
        Returns:
            tuple: (success, message)
        """
        try:
            if not conversion_info.get('keep_only_non_ocr', False):
                return True, "Original file kept as requested"
            
            original_path = conversion_info.get('original_path')
            if not original_path or not os.path.exists(original_path):
                return True, "Original file already removed or not found"
            
            os.remove(original_path)
            self.logger.info(f"Removed original OCR file: {original_path}")
            return True, "Original OCR file removed successfully"
            
        except Exception as e:
            error_message = f"Error removing original file: {str(e)}"
            self.logger.error(error_message)
            return False, error_message
    
    def convert_existing_pdf(self, original_filename, category, dpi=300, keep_only_non_ocr=False):
        """
        Convert an existing PDF from OCR to non-OCR format.
        
        Args:
            original_filename (str): Original filename of the PDF to convert
            category (str): Category of the PDF
            dpi (int): DPI for conversion
            keep_only_non_ocr (bool): Whether to delete the original OCR file
            
        Returns:
            tuple: (success, message, conversion_info)
        """
        try:
            # Find the PDF in the database
            existing_pdf = get_pdf_by_original_filename(original_filename, category)
            if not existing_pdf:
                return False, f"PDF not found: {original_filename} in category {category}", {}
            
            # Construct the file path
            from app.utils.helpers import TEMP_FOLDER
            pdf_filename = existing_pdf['filename']
            pdf_path = os.path.join(TEMP_FOLDER, category, pdf_filename)
            
            if not os.path.exists(pdf_path):
                return False, f"PDF file not found on disk: {pdf_path}", {}
            
            # Perform the conversion
            success, message, conversion_info = self.convert_pdf_ocr_to_non_ocr(
                pdf_path, category, dpi, keep_only_non_ocr
            )
            
            if not success:
                return False, message, conversion_info
            
            # Update database
            db_success, db_message = self.update_database_after_conversion(
                conversion_info, category, original_filename
            )
            if not db_success:
                self.logger.warning(f"Database update failed: {db_message}")
            
            # Update vector database
            vector_success, vector_message = self.update_vector_database_after_conversion(
                conversion_info, category, original_filename
            )
            if not vector_success:
                self.logger.warning(f"Vector database update failed: {vector_message}")
            
            # Cleanup if requested
            cleanup_success, cleanup_message = self.cleanup_original_file(conversion_info)
            if not cleanup_success:
                self.logger.warning(f"Cleanup failed: {cleanup_message}")
            
            return True, message, conversion_info
            
        except Exception as e:
            error_message = f"Error converting existing PDF: {str(e)}"
            self.logger.error(error_message)
            return False, error_message, {}


# Global service instance
_ocr_service = None

def get_ocr_conversion_service():
    """Get the global OCR conversion service instance."""
    global _ocr_service
    if _ocr_service is None:
        _ocr_service = OCRConversionService()
    return _ocr_service
