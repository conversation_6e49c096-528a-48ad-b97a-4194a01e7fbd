#!/usr/bin/env python3
"""
Database Migration Script: Transition to Database-Centric Architecture

This script migrates the ERDB system from the complex three-system approach
(database + filesystem + vector storage) to a pure database-centric architecture.

Changes:
1. Add BLOB columns for PDF content and cover images
2. Add file hash column for duplicate detection
3. Migrate existing data from filesystem to database BLOBs
4. Clean up vector database files
5. Update schema for optimized database-only operations
"""

import sys
import os
import sqlite3
import hashlib
import shutil
from datetime import datetime

# Add the app directory to the Python path
sys.path.append('.')

def calculate_file_hash(file_path):
    """Calculate SHA-256 hash of a file"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {e}")
        return None

def backup_current_database():
    """Create backup of current database before migration"""
    backup_name = f"erdb_main_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    try:
        shutil.copy2('erdb_main.db', backup_name)
        print(f"✅ Database backed up to: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ Error creating database backup: {e}")
        return None

def create_new_schema():
    """Create new database schema optimized for database-centric architecture"""
    print("\n🔄 CREATING NEW DATABASE SCHEMA")
    print("=" * 50)
    
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    try:
        # Add new columns to pdf_documents table
        print("Adding new columns to pdf_documents table...")
        
        # Add file_hash column for duplicate detection
        try:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN file_hash TEXT')
            print("  ✅ Added file_hash column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("  ℹ️  file_hash column already exists")
            else:
                raise
        
        # Add pdf_content_blob column for storing PDF files
        try:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN pdf_content_blob BLOB')
            print("  ✅ Added pdf_content_blob column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("  ℹ️  pdf_content_blob column already exists")
            else:
                raise
        
        # Add cover_image_blob column for storing cover images
        try:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN cover_image_blob BLOB')
            print("  ✅ Added cover_image_blob column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("  ℹ️  cover_image_blob column already exists")
            else:
                raise
        
        # Add cover_image_format column to store image format (PNG, JPEG, etc.)
        try:
            cursor.execute('ALTER TABLE pdf_documents ADD COLUMN cover_image_format TEXT DEFAULT "PNG"')
            print("  ✅ Added cover_image_format column")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("  ℹ️  cover_image_format column already exists")
            else:
                raise
        
        # Create index on file_hash for fast duplicate detection
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pdf_documents_file_hash ON pdf_documents(file_hash)')
            print("  ✅ Created index on file_hash")
        except Exception as e:
            print(f"  ⚠️  Index creation warning: {e}")
        
        # Create index on original_filename for fast searches
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pdf_documents_original_filename ON pdf_documents(original_filename)')
            print("  ✅ Created index on original_filename")
        except Exception as e:
            print(f"  ⚠️  Index creation warning: {e}")
        
        # Create categories table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("  ✅ Created/verified categories table")
        
        conn.commit()
        print("✅ Database schema migration completed successfully!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error during schema migration: {e}")
        raise
    finally:
        conn.close()

def migrate_existing_data():
    """Migrate existing filesystem data to database BLOBs"""
    print("\n📦 MIGRATING EXISTING DATA TO DATABASE")
    print("=" * 50)
    
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    try:
        # Get all existing PDF documents
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at
            FROM pdf_documents
            WHERE pdf_content_blob IS NULL
            ORDER BY created_at DESC
        ''')
        
        records = cursor.fetchall()
        print(f"Found {len(records)} records to migrate")
        
        migrated_count = 0
        error_count = 0
        
        for record in records:
            pdf_id, filename, original_filename, category, created_at = record
            print(f"\nMigrating: {original_filename} (ID: {pdf_id})")
            
            # Find the filesystem file
            possible_paths = [
                os.path.join('data', 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                os.path.join('data', 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                os.path.join('data', 'temp', category, filename.replace('.pdf', ''), filename),
                os.path.join('data', category, f'non_ocr_{filename}'),
                os.path.join('data', category, f'ocr_{filename}'),
                os.path.join('data', category, filename),
            ]
            
            file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    file_path = path
                    break
            
            if file_path:
                try:
                    # Read PDF file content
                    with open(file_path, 'rb') as f:
                        pdf_content = f.read()
                    
                    # Calculate file hash
                    file_hash = calculate_file_hash(file_path)
                    
                    # Extract cover image (first page)
                    cover_image_blob = None
                    cover_image_format = "PNG"
                    
                    try:
                        import fitz  # PyMuPDF
                        doc = fitz.open(stream=pdf_content, filetype="pdf")
                        if len(doc) > 0:
                            page = doc[0]
                            pix = page.get_pixmap()
                            cover_image_blob = pix.tobytes("png")
                            cover_image_format = "PNG"
                        doc.close()
                        print(f"  ✅ Extracted cover image ({len(cover_image_blob)} bytes)")
                    except Exception as e:
                        print(f"  ⚠️  Cover image extraction failed: {e}")
                    
                    # Update database record
                    cursor.execute('''
                        UPDATE pdf_documents 
                        SET pdf_content_blob = ?, file_hash = ?, cover_image_blob = ?, cover_image_format = ?
                        WHERE id = ?
                    ''', (pdf_content, file_hash, cover_image_blob, cover_image_format, pdf_id))
                    
                    migrated_count += 1
                    print(f"  ✅ Migrated to database ({len(pdf_content)} bytes)")
                    
                except Exception as e:
                    error_count += 1
                    print(f"  ❌ Migration error: {e}")
            else:
                error_count += 1
                print(f"  ❌ Filesystem file not found")
        
        conn.commit()
        
        print(f"\n📊 MIGRATION SUMMARY:")
        print(f"Records processed: {len(records)}")
        print(f"Successfully migrated: {migrated_count}")
        print(f"Errors: {error_count}")
        
        if migrated_count > 0:
            print("✅ Data migration completed!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Error during data migration: {e}")
        raise
    finally:
        conn.close()

def cleanup_vector_database():
    """Remove ChromaDB vector database files and directories"""
    print("\n🧹 CLEANING UP VECTOR DATABASE")
    print("=" * 50)
    
    vector_paths = [
        'data/unified_chroma',
        'chroma_db',
        'data/chroma_db',
        'app/chroma_db',
        'chroma',
        'data/chroma'
    ]
    
    removed_count = 0
    
    for path in vector_paths:
        if os.path.exists(path):
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    print(f"  ✅ Removed directory: {path}")
                else:
                    os.remove(path)
                    print(f"  ✅ Removed file: {path}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ Error removing {path}: {e}")
    
    # Also remove any .db files that might be ChromaDB
    for root, dirs, files in os.walk('.'):
        for file in files:
            if 'chroma' in file.lower() and file.endswith('.db'):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    print(f"  ✅ Removed ChromaDB file: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"  ❌ Error removing {file_path}: {e}")
    
    if removed_count > 0:
        print(f"✅ Removed {removed_count} vector database files/directories")
    else:
        print("ℹ️  No vector database files found to remove")

def verify_migration():
    """Verify the migration was successful"""
    print("\n🔍 VERIFYING MIGRATION")
    print("=" * 50)
    
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    try:
        # Check schema
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = cursor.fetchall()
        
        required_columns = ['file_hash', 'pdf_content_blob', 'cover_image_blob', 'cover_image_format']
        existing_columns = [col[1] for col in columns]
        
        print("Database schema verification:")
        for col in required_columns:
            if col in existing_columns:
                print(f"  ✅ {col} column exists")
            else:
                print(f"  ❌ {col} column missing")
        
        # Check data migration
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE pdf_content_blob IS NOT NULL')
        migrated_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pdf_documents')
        total_count = cursor.fetchone()[0]
        
        print(f"\nData migration verification:")
        print(f"  Total records: {total_count}")
        print(f"  Records with PDF content: {migrated_count}")
        print(f"  Migration rate: {(migrated_count/max(total_count,1)*100):.1f}%")
        
        if migrated_count == total_count and total_count > 0:
            print("✅ All records successfully migrated!")
        elif total_count == 0:
            print("ℹ️  No records to migrate")
        else:
            print(f"⚠️  {total_count - migrated_count} records not migrated")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    finally:
        conn.close()

def main():
    """Main migration function"""
    print("🏗️  DATABASE-CENTRIC ARCHITECTURE MIGRATION")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    
    try:
        # Step 1: Backup current database
        backup_file = backup_current_database()
        if not backup_file:
            print("❌ Cannot proceed without database backup")
            return False
        
        # Step 2: Create new schema
        create_new_schema()
        
        # Step 3: Migrate existing data
        migrate_existing_data()
        
        # Step 4: Clean up vector database
        cleanup_vector_database()
        
        # Step 5: Verify migration
        verify_migration()
        
        print(f"\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("The ERDB system has been migrated to database-centric architecture:")
        print("  ✅ PDF files stored as BLOBs in database")
        print("  ✅ Cover images stored as BLOBs in database")
        print("  ✅ File hash-based duplicate detection")
        print("  ✅ Vector database removed")
        print("  ✅ Simplified single-source architecture")
        print(f"  ✅ Database backup: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ MIGRATION FAILED: {e}")
        print("Please restore from backup if needed")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
