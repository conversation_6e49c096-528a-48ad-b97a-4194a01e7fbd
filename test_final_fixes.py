#!/usr/bin/env python3

"""
Test script to verify the final fixes for database-centric architecture:
1. No timestamp prefix in filenames
2. No filesystem file creation during upload
3. Proper duplicate detection using original filenames
"""

import sqlite3
import os
import tempfile
from datetime import datetime

def test_no_timestamp_prefix():
    """Test that filenames don't have timestamp prefixes"""
    print("🔍 Testing filename handling (no timestamp prefix)...")
    
    try:
        from app.utils.database_centric_helpers import store_pdf_in_database
        
        # Test PDF content
        test_content = b"Test PDF content for filename test"
        original_filename = "CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf"
        
        # Store PDF
        result = store_pdf_in_database(
            pdf_content=test_content,
            original_filename=original_filename,
            category="TEST",
            file_size=len(test_content),
            page_count=1
        )
        
        if result['success']:
            stored_filename = result['filename']
            print(f"  ✅ Original filename: {original_filename}")
            print(f"  ✅ Stored filename: {stored_filename}")
            
            # Check if filename has timestamp prefix
            if stored_filename == original_filename:
                print("  ✅ No timestamp prefix - using original filename directly")
                return True, result['pdf_id']
            else:
                print(f"  ❌ Filename has been modified: {stored_filename}")
                return False, None
        else:
            print(f"  ❌ Storage failed: {result['message']}")
            return False, None
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False, None

def test_no_filesystem_creation():
    """Test that no filesystem files are created during upload"""
    print("\n🔍 Testing filesystem file creation (should be none)...")
    
    try:
        # Check if temp directories exist and are empty
        temp_dirs_to_check = [
            "./data/temp/TEST",
            "./data/temp/TEST/CANOPY_INTERNATIONAL_VOL_1_NO_1",
            "./data/temp/TEST/CANOPY_INTERNATIONAL_VOL_1_NO_1/pdf_images",
            "./data/temp/TEST/CANOPY_INTERNATIONAL_VOL_1_NO_1/pdf_tables"
        ]
        
        filesystem_files_found = False
        
        for dir_path in temp_dirs_to_check:
            if os.path.exists(dir_path):
                files = os.listdir(dir_path)
                if files:
                    print(f"  ❌ Found files in {dir_path}: {files}")
                    filesystem_files_found = True
                else:
                    print(f"  ✅ Directory {dir_path} exists but is empty")
            else:
                print(f"  ✅ Directory {dir_path} does not exist (good)")
        
        if not filesystem_files_found:
            print("  ✅ No filesystem files created during upload")
            return True
        else:
            print("  ❌ Filesystem files were created during upload")
            return False
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def test_duplicate_detection_with_original_filename():
    """Test duplicate detection using original filenames"""
    print("\n🔍 Testing duplicate detection with original filenames...")
    
    try:
        from app.utils.database_centric_helpers import store_pdf_in_database, check_duplicate_by_filename
        
        # Store first file
        original_filename = "DUPLICATE_TEST_FILE.pdf"
        test_content_1 = b"First version of test PDF content"
        
        result1 = store_pdf_in_database(
            pdf_content=test_content_1,
            original_filename=original_filename,
            category="TEST",
            file_size=len(test_content_1),
            page_count=1
        )
        
        if not result1['success']:
            print(f"  ❌ Failed to store first file: {result1['message']}")
            return False
        
        print(f"  ✅ First file stored with filename: {result1['filename']}")
        
        # Store second file with same original filename
        test_content_2 = b"Second version of test PDF content"
        
        result2 = store_pdf_in_database(
            pdf_content=test_content_2,
            original_filename=original_filename,  # Same original filename
            category="TEST",
            file_size=len(test_content_2),
            page_count=1
        )
        
        if not result2['success']:
            print(f"  ❌ Failed to store second file: {result2['message']}")
            return False
        
        print(f"  ✅ Second file stored with filename: {result2['filename']}")
        
        # Test duplicate detection
        duplicate_result = check_duplicate_by_filename(original_filename, "TEST")
        
        if duplicate_result['is_duplicate'] and duplicate_result['duplicate_count'] >= 2:
            print(f"  ✅ Duplicate detection working: found {duplicate_result['duplicate_count']} files")
            print(f"  ✅ All files have original filename: {original_filename}")
            
            # Show the different stored filenames
            for i, dup in enumerate(duplicate_result['duplicates']):
                print(f"    File {i+1}: stored as '{dup['filename']}', original: '{dup['original_filename']}'")
            
            return True, [result1['pdf_id'], result2['pdf_id']]
        else:
            print(f"  ❌ Duplicate detection failed: {duplicate_result}")
            return False, None
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False, None

def cleanup_test_data(pdf_ids):
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Delete test records
        cursor.execute("DELETE FROM pdf_documents WHERE category = 'TEST'")
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"  ✅ Cleaned up {deleted_count} test records")
        
    except Exception as e:
        print(f"  ❌ Cleanup failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Testing Final Database-Centric Architecture Fixes")
    print("=" * 60)
    
    # Test 1: No timestamp prefix
    filename_ok, pdf_id1 = test_no_timestamp_prefix()
    
    # Test 2: No filesystem creation
    filesystem_ok = test_no_filesystem_creation()
    
    # Test 3: Duplicate detection with original filenames
    duplicate_ok, pdf_ids = test_duplicate_detection_with_original_filename()
    
    # Cleanup
    all_pdf_ids = []
    if pdf_id1:
        all_pdf_ids.append(pdf_id1)
    if pdf_ids:
        all_pdf_ids.extend(pdf_ids)
    
    cleanup_test_data(all_pdf_ids)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 FINAL TEST SUMMARY:")
    
    if filename_ok:
        print("✅ Filenames use original names (no timestamp prefix)")
    else:
        print("❌ Filenames still have timestamp prefix")
    
    if filesystem_ok:
        print("✅ No filesystem files created during upload")
    else:
        print("❌ Filesystem files are still being created")
    
    if duplicate_ok:
        print("✅ Duplicate detection works with original filenames")
    else:
        print("❌ Duplicate detection has issues")
    
    if filename_ok and filesystem_ok and duplicate_ok:
        print("\n🎉 ALL FIXES ARE WORKING CORRECTLY!")
        print("✅ Database-centric architecture is fully implemented")
        print("✅ No timestamp prefixes in filenames")
        print("✅ No filesystem file creation")
        print("✅ Proper duplicate detection")
    else:
        print("\n❌ Some issues remain. Please check the failed tests above.")

if __name__ == "__main__":
    main()
