#!/usr/bin/env python3
"""
Comprehensive database analysis to understand duplicate detection issues
"""

import sqlite3
import os
from datetime import datetime

def check_sqlite_database():
    """Check the main SQLite database"""
    print("📊 1. SQLite Database (erdb_main.db):")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Check for CANOPY_INTERNATIONAL files
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at 
            FROM pdf_documents 
            WHERE original_filename LIKE '%CANOPY_INTERNATIONAL%' 
            ORDER BY created_at DESC
        ''')
        
        rows = cursor.fetchall()
        print(f"Records found: {len(rows)}")
        
        for row in rows:
            print(f"  ID={row[0]}, filename={row[1]}")
            print(f"    original_filename={row[2]}")
            print(f"    category={row[3]}, created_at={row[4]}")
            print()
        
        # Check total records in CANOPY category
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE category = "CANOPY"')
        total_canopy = cursor.fetchone()[0]
        print(f"Total CANOPY records: {total_canopy}")
        
        conn.close()
        return rows
        
    except Exception as e:
        print(f"Error: {e}")
        return []

def check_chroma_database():
    """Check the ChromaDB database"""
    print("\n📊 2. ChromaDB (data/unified_chroma/chroma.sqlite3):")
    print("-" * 40)
    
    chroma_path = 'data/unified_chroma/chroma.sqlite3'
    
    if not os.path.exists(chroma_path):
        print("ChromaDB file not found")
        return
    
    try:
        conn = sqlite3.connect(chroma_path)
        cursor = conn.cursor()
        
        # Get table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"Tables: {tables}")
        
        # Check collections if exists
        if 'collections' in tables:
            cursor.execute('SELECT * FROM collections')
            collections = cursor.fetchall()
            print(f"Collections: {len(collections)} found")
            for col in collections:
                print(f"  {col}")
        
        # Check embeddings if exists
        if 'embeddings' in tables:
            cursor.execute('SELECT COUNT(*) FROM embeddings')
            count = cursor.fetchone()[0]
            print(f"Embeddings: {count} records")
            
            # Try to find CANOPY_INTERNATIONAL related embeddings
            cursor.execute("SELECT * FROM embeddings WHERE document LIKE '%CANOPY_INTERNATIONAL%' LIMIT 5")
            canopy_embeddings = cursor.fetchall()
            print(f"CANOPY_INTERNATIONAL embeddings: {len(canopy_embeddings)}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

def find_all_database_files():
    """Find all database files in the project"""
    print("\n📊 3. All Database Files:")
    print("-" * 40)
    
    db_extensions = ['.db', '.sqlite', '.sqlite3']
    db_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip hidden directories and common non-relevant directories
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__']]
        
        for file in files:
            if any(file.endswith(ext) for ext in db_extensions):
                full_path = os.path.join(root, file)
                size = os.path.getsize(full_path)
                db_files.append((full_path, size))
    
    print(f"Found {len(db_files)} database files:")
    for db_file, size in sorted(db_files):
        print(f"  {db_file} ({size:,} bytes)")
    
    return db_files

def check_manage_files_source():
    """Try to understand where the Manage Files interface gets its data"""
    print("\n📊 4. Manage Files Interface Source:")
    print("-" * 40)
    
    # Look for routes or functions that handle the manage files page
    try:
        # Check if there's a specific route for manage files
        with open('app.py', 'r') as f:
            content = f.read()
            
        if 'manage' in content.lower() and 'files' in content.lower():
            print("Found 'manage files' references in app.py")
            
            # Look for the specific route
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'manage' in line.lower() and ('files' in line.lower() or 'route' in line.lower()):
                    print(f"  Line {i+1}: {line.strip()}")
                    # Show a few lines of context
                    for j in range(max(0, i-2), min(len(lines), i+5)):
                        if j != i:
                            print(f"    {j+1}: {lines[j].strip()}")
                    print()
        else:
            print("No obvious 'manage files' references found in app.py")
            
    except Exception as e:
        print(f"Error reading app.py: {e}")

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE DATABASE ANALYSIS")
    print("=" * 60)
    
    # Check all databases
    sqlite_records = check_sqlite_database()
    check_chroma_database()
    find_all_database_files()
    check_manage_files_source()
    
    # Summary
    print("\n📋 ANALYSIS SUMMARY:")
    print("=" * 60)
    
    if sqlite_records:
        print(f"✅ Found {len(sqlite_records)} CANOPY_INTERNATIONAL records in SQLite")
        
        # Check for actual duplicates
        original_names = [record[2] for record in sqlite_records]
        unique_names = set(original_names)
        
        if len(original_names) != len(unique_names):
            print("❌ DUPLICATES DETECTED in SQLite database!")
            for name in unique_names:
                count = original_names.count(name)
                if count > 1:
                    print(f"  '{name}' appears {count} times")
        else:
            print("✅ No duplicates found in SQLite database")
    else:
        print("❌ No CANOPY_INTERNATIONAL records found in SQLite")
    
    print("\nNext steps:")
    print("1. Check if duplicates exist in SQLite database")
    print("2. Identify why duplicate detection didn't prevent them")
    print("3. Clean up existing duplicates")
    print("4. Ensure synchronization between databases")
