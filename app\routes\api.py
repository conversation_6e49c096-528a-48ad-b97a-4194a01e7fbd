from flask import Blueprint, request, jsonify, current_app
import os
import logging
import json
import requests
from werkzeug.utils import secure_filename
from app.services.query_service import query_category
from app.services.embedding_service import embed_file, scrape_url
from app.utils import helpers as utils
from app.utils import database as db_utils
from app.services import geo_service as geo_utils
from app.services import geo_analytics as geoip_analytics
from app.services.optimized_vector_db import get_vector_db, OLLAMA_BASE_URL
from app.services.vector_db import _chroma_cache
from app.utils.helpers import list_categories, delete_file, check_duplicate_pdf
from app.services.ocr_conversion_service import get_ocr_conversion_service
import ollama
from scripts.setup.create_temp_dirs import create_temp_directories
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.embedding_db import embed_file_db_first, scrape_url_db_first
from app.routes.auth import admin_required, function_permission_required
from app.utils.config import save_default_models, update_env_file
from flask_wtf import CSRFProtect

api_bp = Blueprint('api_bp', __name__, url_prefix='/api')

logger = logging.getLogger(__name__)
csrf = CSRFProtect()


@api_bp.route('/check_duplicate', methods=['POST'])
@csrf.exempt
@function_permission_required('upload_files')
def check_duplicate():
    try:
        # Get file and category from form data or JSON
        if request.content_type == 'application/json':
            data = request.get_json()
            filename = data.get('filename')
            category = data.get('category')
            file_obj = None
            if not filename:
                return jsonify({'error': 'Filename is required.'}), 400
        else:  # Handle multipart/form-data
            file_obj = request.files.get('file')
            filename = None
            if file_obj and file_obj.filename:
                filename = secure_filename(file_obj.filename)
                # Reset file pointer to beginning in case it was read already
                file_obj.seek(0)
            category = request.form.get('category')

        if not filename:
            return jsonify({'error': 'No file provided or invalid filename'}), 400
        if not category:
            return jsonify({'error': 'Category is required.'}), 400

        # If we have a file object, use it, otherwise just check by filename
        if file_obj:
            is_duplicate, duplicate_info = check_duplicate_pdf(file_obj, category)
        else:
            # If no file object, just check if a file with this name exists in the category
            from app.utils.helpers import TEMP_FOLDER
            file_path = os.path.join(TEMP_FOLDER, category, filename)
            is_duplicate = os.path.exists(file_path)
            duplicate_info = None

        response_data = {'is_duplicate': is_duplicate}
        
        if is_duplicate and duplicate_info:
            response_data['duplicate_info'] = duplicate_info
            if duplicate_info.get('type') == 'filename_match':
                response_data['message'] = f"A file with the same name already exists in category '{category}'"
            elif duplicate_info.get('type') == 'content_match':
                response_data['message'] = f"A file with identical content already exists in category '{category}'"
            else:
                response_data['message'] = f"A duplicate file was found in category '{category}'"
        elif is_duplicate:
            response_data['message'] = f"A file with the same name already exists in category '{category}'"

        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in check_duplicate: {str(e)}", exc_info=True)
        return jsonify({'error': f'Error checking for duplicate: {str(e)}'}), 500

@api_bp.route('/upload', methods=['POST'])
@function_permission_required('upload_files')
def upload_file():
    if 'file' not in request.files and 'url' not in request.form:
        return jsonify({'error': 'No file or URL provided'}), 400

    category = request.form.get('category', 'default')
    if not category.strip():
        return jsonify({'error': 'Category cannot be empty'}), 400

    if 'file' in request.files and request.files['file'].filename != '':
        file = request.files['file']
        filename = secure_filename(file.filename)
        if not filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        # Check for duplicates first
        duplicate_action = request.form.get('duplicate_action', 'reject')
        force_update = duplicate_action == 'replace'

        try:
            success, message = embed_file_db_first(
                file,
                category,
                source_url=request.form.get('source_url'),
                use_vision=request.form.get('use_vision'),
                filter_sensitivity=request.form.get('filter_sensitivity'),
                max_images=request.form.get('max_images'),
                force_update=force_update
            )

            if success:
                response_data = {'message': f'File "{filename}" uploaded and embedded successfully in category "{category}"'}

                # Check if OCR conversion was requested
                convert_to_non_ocr = request.form.get('convert_to_non_ocr', '').lower() == 'true'
                if convert_to_non_ocr:
                    try:
                        conversion_dpi = int(request.form.get('conversion_dpi', 300))
                        keep_only_non_ocr = request.form.get('keep_only_non_ocr', '').lower() == 'true'

                        ocr_service = get_ocr_conversion_service()
                        conv_success, conv_message, conversion_info = ocr_service.convert_existing_pdf(
                            filename, category, conversion_dpi, keep_only_non_ocr
                        )

                        if conv_success:
                            response_data['ocr_conversion'] = {
                                'success': True,
                                'message': conv_message,
                                'converted_filename': conversion_info.get('converted_filename')
                            }
                        else:
                            response_data['ocr_conversion'] = {
                                'success': False,
                                'error': conv_message
                            }
                    except Exception as conv_e:
                        logger.error(f"OCR conversion failed: {conv_e}")
                        response_data['ocr_conversion'] = {
                            'success': False,
                            'error': f'OCR conversion failed: {str(conv_e)}'
                        }

                return jsonify(response_data), 200
            else:
                # If it's a duplicate and action is reject, return 409 Conflict
                if 'duplicate' in message.lower() and not force_update:
                    return jsonify({'error': message, 'is_duplicate': True}), 409
                else:
                    return jsonify({'error': message}), 500

        except Exception as e:
            logger.error(f"Error embedding file: {e}")
            return jsonify({'error': str(e)}), 500

    if 'url' in request.form and request.form['url'].strip() != '':
        url = request.form['url']
        try:
            scrape_url_db_first(url, category)
            return jsonify({'message': f'URL "{url}" scraped and embedded successfully in category "{category}"'}), 200
        except Exception as e:
            logger.error(f"Error scraping URL: {e}")
            return jsonify({'error': str(e)}), 500

    return jsonify({'error': 'No file or URL provided'}), 400

@api_bp.route('/categories', methods=['GET'])
@function_permission_required('view_categories')
def list_categories_route():
    try:
        return jsonify(utils.list_categories_with_details())
    except Exception as e:
        logger.error(f"Error listing categories: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories', methods=['POST'])
@function_permission_required('manage_categories')
def create_category():
    data = request.get_json()
    category_name = data.get('category_name')
    if not category_name:
        return jsonify({'error': 'Category name is required'}), 400
    try:
        from app.utils.helpers import create_category
        success = create_category(category_name)
        if success:
            return jsonify({'message': f'Category "{category_name}" created successfully'}), 201
        else:
            return jsonify({'error': f'Failed to create category "{category_name}"'}), 500
    except Exception as e:
        logger.error(f"Error creating category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories/<old_category>', methods=['PUT'])
@function_permission_required('manage_categories')
def update_category(old_category):
    data = request.get_json()
    new_category = data.get('new_category')
    if not new_category:
        return jsonify({'error': 'New category name is required'}), 400
    try:
        utils.update_category_name(old_category, new_category)
        return jsonify({'message': f'Category "{old_category}" updated to "{new_category}" successfully'}), 200
    except Exception as e:
        logger.error(f"Error updating category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories/<category>', methods=['DELETE'])
@function_permission_required('manage_categories')
def delete_category(category):
    try:
        utils.delete_category_and_files(category)
        return jsonify({'message': f'Category "{category}" deleted successfully'}), 200
    except Exception as e:
        logger.error(f"Error deleting category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files', methods=['GET'])
@function_permission_required('view_files')
def list_files():
    category = request.args.get('category')
    if not category:
        return jsonify({'error': 'Category is required'}), 400
    try:
        files = utils.get_files_in_category(category)
        return jsonify(files)
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files/delete', methods=['POST'])
@function_permission_required('manage_files')
def delete_file_route():
    data = request.get_json()
    filename = data.get('filename')
    category = data.get('category')
    if not filename or not category:
        return jsonify({'error': 'Filename and category are required'}), 400
    try:
        delete_file(category, filename)
        return jsonify({'message': f'File "{filename}" in category "{category}" deleted successfully'}), 200
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files/content', methods=['GET', 'POST'])
@function_permission_required('view_files')
def handle_file_content():
    if request.method == 'GET':
        category = request.args.get('category')
        filename = request.args.get('filename')
        if not category or not filename:
            return jsonify({'error': 'Category and filename are required'}), 400
        try:
            content = utils.get_file_content(category, filename)
            return jsonify({'content': content})
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        category = data.get('category')
        filename = data.get('filename')
        content = data.get('content')
        if not category or not filename or content is None:
            return jsonify({'error': 'Category, filename, and content are required'}), 400
        try:
            utils.update_file_content(category, filename, content)
            return jsonify({'message': 'File content updated successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/query', methods=['POST'])
@function_permission_required('perform_query')
def query():
    data = request.get_json()
    if not data or 'query' not in data or 'category' not in data:
        return jsonify({'error': 'Query and category are required'}), 400

    query_text = data['query']
    category = data['category']
    user_id = 1 # Replace with actual user ID from session

    try:
        result = query_category(query_text, category, user_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models', methods=['GET'])
@admin_required
def get_ollama_models():
    from app.utils.config import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data)
    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Ollama: {e}")
        return jsonify({'error': 'Could not connect to Ollama. Please ensure Ollama is running and accessible.'}), 500
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/models', methods=['GET', 'POST'])
@admin_required
def handle_default_models():
    from app.utils.config import get_models_data
    from app import save_default_models, update_env_file
    if request.method == 'GET':
        try:
            return jsonify(get_models_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        try:
            save_default_models(llm_model=data.get('llm_model'), 
                                embedding_model=data.get('embedding_model'),
                                vision_model=data.get('vision_model'))
            update_env_file(llm_model=data.get('llm_model'), 
                            embedding_model=data.get('embedding_model'),
                            vision_model=data.get('vision_model'))
            return jsonify({'message': 'Default models saved successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/vision_embedding', methods=['GET'])
def handle_vision_embedding_settings():
    from app.utils.config import get_embedding_config_data
    from app import save_default_models
    if request.method == 'GET':
        try:
            return jsonify(get_embedding_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        try:
            save_default_models(use_vision=data.get('use_vision_model'),
                                filter_pdf_images=data.get('filter_pdf_images'),
                                filter_sensitivity=data.get('filter_sensitivity'),
                                max_pdf_images=data.get('max_pdf_images'))
            return jsonify({'message': 'Vision and embedding settings saved successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/vision_embedding_status', methods=['GET'])
def get_vision_embedding_status():
    """Get the global vision model during embedding setting."""
    try:
        # Get the current setting from environment variable
        use_vision_during_embedding = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

        # Return the setting as JSON
        return jsonify({
            "enabled": use_vision_during_embedding,
            "message": "Vision model during embedding is " + ("enabled" if use_vision_during_embedding else "disabled")
        }), 200
    except Exception as e:
        logger.error(f"Error getting vision embedding setting: {str(e)}")
        return jsonify({"error": str(e)}), 500

@api_bp.route('/settings/query_config', methods=['GET', 'POST'])
@admin_required
def handle_query_config():
    from app.utils.config import get_query_config_data
    from app import app, logger, save_default_models, clear_cache
    if request.method == 'GET':
        try:
            return jsonify(get_query_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            preamble = data.get('preamble')
            anti_hallucination_mode = data.get('anti_hallucination_mode')
            anti_hallucination_custom_instructions = data.get('anti_hallucination_custom_instructions')
            prompt_templates = data.get('prompt_templates')
            insufficient_info_phrases = data.get('insufficient_info_phrases')
            followup_question_templates = data.get('followup_question_templates')
            use_vision = data.get('use_vision')

            # --- Extract hallucination detection thresholds (support both flat and nested) ---
            hallucination_detection = data.get('hallucination_detection', {})
            hallucination_threshold_strict = data.get('hallucination_threshold_strict', hallucination_detection.get('threshold_strict'))
            hallucination_threshold_balanced = data.get('hallucination_threshold_balanced', hallucination_detection.get('threshold_balanced'))
            hallucination_threshold_default = data.get('hallucination_threshold_default', hallucination_detection.get('threshold_default'))
            min_statement_length = data.get('min_statement_length', hallucination_detection.get('min_statement_length'))

            # Save the query configuration
            success = save_default_models(
                preamble=preamble,
                anti_hallucination_mode=anti_hallucination_mode,
                anti_hallucination_custom_instructions=anti_hallucination_custom_instructions,
                prompt_templates=prompt_templates,
                insufficient_info_phrases=insufficient_info_phrases,
                followup_question_templates=followup_question_templates,
                use_vision=use_vision,
                hallucination_threshold_strict=hallucination_threshold_strict,
                hallucination_threshold_balanced=hallucination_threshold_balanced,
                hallucination_threshold_default=hallucination_threshold_default,
                min_statement_length=min_statement_length
            )

            if success:
                # Update environment variables
                if preamble is not None:
                    os.environ['QUERY_PREAMBLE'] = preamble

                if anti_hallucination_mode is not None:
                    os.environ['ANTI_HALLUCINATION_MODE'] = anti_hallucination_mode

                if anti_hallucination_custom_instructions is not None:
                    os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = anti_hallucination_custom_instructions

                if prompt_templates is not None:
                    os.environ['PROMPT_TEMPLATES'] = json.dumps(prompt_templates)

                if insufficient_info_phrases is not None:
                    os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(insufficient_info_phrases)

                if followup_question_templates is not None:
                    os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_question_templates)

                if use_vision is not None:
                    use_vision_str = 'true' if use_vision else 'false'
                    os.environ['USE_VISION_MODEL'] = use_vision_str
                    app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
                    logger.info(f"Updated vision model for chat setting: {app.config['USE_VISION_MODEL']}")

                return jsonify({"message": "Query configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save query configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving query configuration: {str(e)}")
            return jsonify({"error": f"Failed to save query configuration: {str(e)}"}), 500

@api_bp.route('/settings/embedding_config', methods=['GET', 'POST'])
@admin_required
def handle_embedding_config():
    from app.utils.config import get_embedding_config_data
    from app import save_default_models
    if request.method == 'GET':
        try:
            return jsonify(get_embedding_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            chunk_size = data.get('chunk_size')
            chunk_overlap = data.get('chunk_overlap')
            extract_tables = data.get('extract_tables')
            extract_images = data.get('extract_images')
            use_vision_model = data.get('use_vision_model')
            filter_sensitivity = data.get('filter_sensitivity')
            max_images = data.get('max_images')
            embedding_model = data.get('embedding_model')
            batch_size = data.get('batch_size')
            processing_threads = data.get('processing_threads')

            # Save the embedding configuration
            success = save_default_models(
                embedding_model=embedding_model,
                use_vision_during_embedding=use_vision_model,
                filter_sensitivity=filter_sensitivity,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                extract_tables=extract_tables,
                extract_images=extract_images,
                max_images=max_images,
                batch_size=batch_size,
                processing_threads=processing_threads
            )

            if success:
                # Update environment variables
                if embedding_model is not None:
                    os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                    _chroma_cache.clear()

                if use_vision_model is not None:
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_model else 'false'

                if filter_sensitivity is not None:
                    os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

                if max_images is not None:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_images)

                if chunk_size is not None:
                    os.environ['EMBEDDING_CHUNK_SIZE'] = str(chunk_size)

                if chunk_overlap is not None:
                    os.environ['EMBEDDING_CHUNK_OVERLAP'] = str(chunk_overlap)

                if batch_size is not None:
                    os.environ['EMBEDDING_BATCH_SIZE'] = str(batch_size)

                if processing_threads is not None:
                    os.environ['EMBEDDING_PROCESSING_THREADS'] = str(processing_threads)

                return jsonify({"message": "Embedding configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save embedding configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving embedding configuration: {str(e)}")
            return jsonify({"error": f"Failed to save embedding configuration: {str(e)}"}), 500

@api_bp.route('/settings/unified_config', methods=['POST'])
@admin_required
def save_unified_config():
    from app import save_default_models
    data = request.get_json()
    try:
        save_default_models(**data)
        return jsonify({'message': 'Configuration saved successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models', methods=['GET'])
@admin_required
def get_llm_models():
    from app import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['models'])
    except Exception as e:
        logger.error(f"Error fetching LLM models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/embeddings', methods=['GET'])
@admin_required
def get_embedding_models():
    from app import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['embeddings'])
    except Exception as e:
        logger.error(f"Error fetching embedding models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/vision_models', methods=['GET'])
@admin_required
def get_vision_models():
    from app import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['vision_models'])
    except Exception as e:
        logger.error(f"Error fetching vision models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/extract_locations', methods=['POST'])
@admin_required
@function_permission_required('extract_locations')
def extract_locations_from_text():
    from app import logger
    try:
        data = request.get_json()
        text = data.get('text', '')
        if not text:
            return jsonify({'success': False, 'message': 'No text provided'}), 400

        locations = geo_utils.extract_locations_from_text(text)
        geocoded_locations = []

        for location in locations:
            geocoding_result = geo_utils.geocode_location(location['name'])
            if geocoding_result and geocoding_result.get('latitude') and geocoding_result.get('longitude'):
                location.update({
                    'latitude': geocoding_result['latitude'],
                    'longitude': geocoding_result['longitude'],
                    'country': geocoding_result['country'],
                    'state': geocoding_result.get('state'),
                    'city': geocoding_result['city'],
                    'municipality': geocoding_result.get('municipality'),
                    'barangay': geocoding_result.get('barangay')
                })

            # Only include locations with valid coordinates
            if location.get('latitude') and location.get('longitude'):
                geocoded_locations.append(location)

        return jsonify({
            'success': True,
            'locations': geocoded_locations,
            'count': len(geocoded_locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error extracting locations: {str(e)}'
        }), 500


@api_bp.route('/generate-html', methods=['POST'])
@admin_required
def generate_html_route():
    from app import html_generator
    data = request.get_json()
    if not data or 'content' not in data:
        return jsonify({'error': 'No content provided'}), 400
    
@api_bp.route('/locations')
@function_permission_required('ai_analytics')
def api_locations():
    """API endpoint to get location data."""
    try:
        category = request.args.get('category')

        if category:
            from app.utils.database import get_locations_by_category
            locations = get_locations_by_category(category)
        else:
            from app.utils.database import get_all_extracted_locations
            locations = get_all_extracted_locations(include_sources=True)

        return jsonify({
            'success': True,
            'locations': locations,
            'count': len(locations)
        })
    except Exception as e:
        logger.error(f"Error retrieving locations via API: {str(e)}")
        return jsonify({'error': 'Failed to retrieve locations'}), 500

@api_bp.route('/location_statistics')
@function_permission_required('ai_analytics')
def api_location_statistics():
    """API endpoint to get location statistics."""
    try:
        from app.utils.database import get_location_statistics
        statistics = get_location_statistics()
        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error retrieving location statistics: {str(e)}")
        return jsonify({'error': 'Failed to retrieve statistics'}), 500

@api_bp.route('/locations/<int:location_id>', methods=['DELETE'])
@function_permission_required('ai_analytics')
def api_delete_location(location_id):
    """API endpoint to delete a single location."""
    try:
        from app.utils.database import delete_location_by_id

        success = delete_location_by_id(location_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Location deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to delete location'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting location {location_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting location: {str(e)}'
        }), 500

@api_bp.route('/locations/bulk-delete', methods=['POST'])
@function_permission_required('ai_analytics')
def api_bulk_delete_locations():
    """API endpoint to delete multiple locations."""
    try:
        from app.utils.database import delete_location_by_id

        data = request.get_json()
        location_ids = data.get('location_ids', [])

        if not location_ids:
            return jsonify({
                'success': False,
                'message': 'No location IDs provided'
            }), 400

        deleted_count = 0
        failed_count = 0

        for location_id in location_ids:
            try:
                if delete_location_by_id(int(location_id)):
                    deleted_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"Error deleting location {location_id}: {str(e)}")
                failed_count += 1

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'failed_count': failed_count,
            'message': f'Deleted {deleted_count} location(s)' + (f', {failed_count} failed' if failed_count > 0 else '')
        })

    except Exception as e:
        logger.error(f"Error in bulk delete: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting locations: {str(e)}'
        }), 500

# NOTE: This route originally conflicted with another '/extract_locations' route.
# It uses the more detailed LocationExtractor.
@api_bp.route('/extract-locations-detailed', methods=['POST'])
def api_extract_locations():
    """API endpoint to extract locations from text using the detailed LocationExtractor."""
    try:
        data = request.get_json()
        text = data.get('text', '')
        sources = data.get('sources', [])
        filter_philippine = data.get('filter_philippine', True)
        admin_levels_only = data.get('admin_levels_only', True)

        if not text:
            return jsonify({
                'success': False,
                'message': 'No text provided'
            }), 400

        # Import location extractor
        from app.services.location_extractor import LocationExtractor

        # Initialize extractor
        extractor = LocationExtractor()

        # Extract locations from text
        locations = extractor.extract_locations_from_text(text)

        # Filter for Philippine administrative divisions if requested
        if filter_philippine and admin_levels_only:
            locations = [
                loc for loc in locations
                if loc.get('location_type') in ['municipality', 'city', 'barangay'] or
                   (loc.get('administrative_level') in ['municipality', 'city', 'barangay'])
            ]

        # Geocode locations that don't have coordinates
        geocoded_locations = []
        for location in locations:
            if not location.get('latitude') or not location.get('longitude'):
                geocoding_result = extractor.geocode_location(location['location_text'])
                if geocoding_result and geocoding_result.get('status') == 'success':
                    location.update({
                        'latitude': geocoding_result['latitude'],
                        'longitude': geocoding_result['longitude'],
                        'geocoded_address': geocoding_result['formatted_address'],
                        'country': geocoding_result['country'],
                        'region': geocoding_result['region'],
                        'city': geocoding_result['city'],
                        'municipality': geocoding_result.get('municipality'),
                        'barangay': geocoding_result.get('barangay')
                    })

            # Only include locations with valid coordinates
            if location.get('latitude') and location.get('longitude'):
                geocoded_locations.append(location)

        return jsonify({
            'success': True,
            'locations': geocoded_locations,
            'count': len(geocoded_locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error extracting locations: {str(e)}'
        }), 500


@api_bp.route('/convert_ocr_pdf', methods=['POST'])
@function_permission_required('upload_files')
def convert_ocr_pdf():
    """
    Convert an existing OCR PDF to non-OCR format.

    Expected JSON payload:
    {
        "original_filename": "document.pdf",
        "category": "CANOPY",
        "dpi": 300,
        "keep_only_non_ocr": false
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'JSON payload required'}), 400

        original_filename = data.get('original_filename')
        category = data.get('category')
        dpi = data.get('dpi', 300)
        keep_only_non_ocr = data.get('keep_only_non_ocr', False)

        if not original_filename:
            return jsonify({'error': 'original_filename is required'}), 400
        if not category:
            return jsonify({'error': 'category is required'}), 400

        # Validate DPI
        try:
            dpi = int(dpi)
            if dpi < 72 or dpi > 600:
                return jsonify({'error': 'DPI must be between 72 and 600'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'DPI must be a valid integer'}), 400

        # Get OCR conversion service
        ocr_service = get_ocr_conversion_service()

        # Perform the conversion
        success, message, conversion_info = ocr_service.convert_existing_pdf(
            original_filename, category, dpi, keep_only_non_ocr
        )

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'conversion_info': {
                    'original_filename': conversion_info.get('original_filename'),
                    'converted_filename': conversion_info.get('converted_filename'),
                    'conversion_dpi': conversion_info.get('conversion_dpi'),
                    'keep_only_non_ocr': conversion_info.get('keep_only_non_ocr'),
                    'ocr_detection': conversion_info.get('ocr_detection', {})
                }
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400

    except Exception as e:
        logger.error(f"Error in OCR conversion API: {str(e)}")
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500


@api_bp.route('/detect_ocr_pdf', methods=['POST'])
@function_permission_required('upload_files')
def detect_ocr_pdf_api():
    """
    Detect if a PDF contains OCR text layers.

    Expected JSON payload:
    {
        "original_filename": "document.pdf",
        "category": "CANOPY"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'JSON payload required'}), 400

        original_filename = data.get('original_filename')
        category = data.get('category')

        if not original_filename:
            return jsonify({'error': 'original_filename is required'}), 400
        if not category:
            return jsonify({'error': 'category is required'}), 400

        # Find the PDF in the database
        from app.utils.content_db import get_pdf_by_original_filename
        existing_pdf = get_pdf_by_original_filename(original_filename, category)
        if not existing_pdf:
            return jsonify({'error': f'PDF not found: {original_filename} in category {category}'}), 404

        # Construct the file path
        from app.utils.helpers import TEMP_FOLDER
        pdf_filename = existing_pdf['filename']
        pdf_path = os.path.join(TEMP_FOLDER, category, pdf_filename)

        if not os.path.exists(pdf_path):
            return jsonify({'error': f'PDF file not found on disk: {pdf_path}'}), 404

        # Detect OCR content
        from app.services.pdf_processor import detect_ocr_pdf
        ocr_detection = detect_ocr_pdf(pdf_path)

        return jsonify({
            'success': True,
            'original_filename': original_filename,
            'category': category,
            'ocr_detection': ocr_detection
        }), 200

    except Exception as e:
        logger.error(f"Error in OCR detection API: {str(e)}")
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

