#!/usr/bin/env python3
"""
Test the web interface list_files function directly
"""

import sys
import os
sys.path.append('.')

def test_list_files_function():
    """Test the list_files function directly"""
    
    print('=== TESTING WEB INTERFACE LIST_FILES FUNCTION ===')
    
    try:
        # Import the necessary modules
        from app.utils.content_db import get_db_connection
        import logging
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # Constants from the app
        TEMP_FOLDER = 'data'
        
        # Replicate the exact logic from the fixed list_files function
        files_data = {}
        
        # Get files from database first
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')

        db_records = cursor.fetchall()
        conn.close()

        logger.info(f"Found {len(db_records)} files in database")

        # Group by category
        db_files_by_category = {}
        for record in db_records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record

            if category not in db_files_by_category:
                db_files_by_category[category] = []

            db_files_by_category[category].append({
                'id': pdf_id,
                'filename': filename,
                'original_filename': original_filename,
                'category': category,
                'created_at': created_at,
                'file_size': file_size,
                'page_count': page_count
            })

        # Process each category from database
        for category, db_file_list in db_files_by_category.items():
            files = []

            for db_file in db_file_list:
                filename = db_file['filename']
                original_filename = db_file['original_filename']

                # Find filesystem path - improved logic for current structure
                fs_path = None
                
                # Current structure: data/temp/CATEGORY/TIMESTAMP_FILENAME/non_ocr_TIMESTAMP_FILENAME.pdf
                possible_paths = [
                    # Current structure with subdirectories
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                    
                    # Legacy structure - direct files in category folder
                    os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, filename),
                    
                    # Alternative temp structure
                    os.path.join(TEMP_FOLDER, '_temp', category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, filename),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        fs_path = path
                        logger.debug(f"Found filesystem file: {path}")
                        break

                if fs_path:
                    # Create file data structure
                    file_data = {
                        "original_filename": original_filename,
                        "source": os.path.basename(fs_path),
                        "type": "pdf",
                        "database_id": db_file['id'],
                        "created_at": db_file['created_at'],
                        "file_size": db_file['file_size'],
                        "page_count": db_file['page_count']
                    }

                    files.append(file_data)
                    logger.debug(f"Added database file: {category}/{original_filename}")
                else:
                    logger.warning(f"Database file not found in filesystem: {category}/{filename}")

            if files:
                files_data[category] = files

        # Print results
        print(f'\n=== RESULTS ===')
        print(f'Total categories with files: {len(files_data)}')
        
        for category, files in files_data.items():
            print(f'\nCategory: {category}')
            print(f'  Files found: {len(files)}')
            for file in files:
                print(f'    - {file["original_filename"]} (ID: {file["database_id"]})')
        
        if files_data:
            print('\n✅ SUCCESS: Web interface should now display files!')
            print(f'Total files that will be displayed: {sum(len(files) for files in files_data.values())}')
        else:
            print('\n❌ PROBLEM: No files will be displayed in web interface')
            
        return files_data
        
    except Exception as e:
        print(f'Error testing list_files function: {e}')
        import traceback
        traceback.print_exc()
        return {}

if __name__ == '__main__':
    test_list_files_function()
