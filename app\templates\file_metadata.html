{% extends "admin_base.html" %}

{% block title %}File Metadata for {{ filename }}{% endblock %}

{% block head %}
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>

    <!-- Include dark-mode.css for consistent dark mode styling -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* Dark mode styles for file metadata page */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #1f2937 !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }
        .dark .text-gray-900 { color: #f3f4f6 !important; }

        /* Border colors in dark mode */
        .dark .border-gray-200 { border-color: #4b5563 !important; }
        .dark .border-gray-300 { border-color: #6b7280 !important; }

        /* Info alert in dark mode */
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .border-blue-500 { border-color: #3b82f6 !important; }
        .dark .text-blue-700 { color: #93c5fd !important; }
        .dark .text-blue-500 { color: #60a5fa !important; }

        /* Success alert in dark mode */
        .dark .bg-green-50 { background-color: #064e3b !important; }
        .dark .border-green-500 { border-color: #10b981 !important; }
        .dark .text-green-700 { color: #6ee7b7 !important; }

        /* Badge styles in dark mode */
        .dark .bg-blue-100 { background-color: #1e40af !important; }
        .dark .text-blue-800 { color: #bfdbfe !important; }
        .dark .bg-green-100 { background-color: #065f46 !important; }
        .dark .text-green-800 { color: #a7f3d0 !important; }
        .dark .bg-red-100 { background-color: #7f1d1d !important; }
        .dark .text-red-800 { color: #fca5a5 !important; }

        /* Hover effects in dark mode */
        .dark .hover\:bg-gray-50:hover { background-color: #4b5563 !important; }

        /* Link colors in dark mode */
        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .hover\:underline:hover { text-decoration: underline !important; }
    </style>
{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-100">File Metadata for {{ filename }}</h1>
            <div class="flex space-x-4 items-center">
                <a href="{{ url_for('list_files') }}" class="text-blue-600 dark:text-blue-400 hover:underline">&larr; Back to Files</a>
                <a href="{{ url_for('admin.admin_dashboard') }}" class="text-blue-600 dark:text-blue-400 hover:underline">Admin Dashboard</a>
                <button id="theme-toggle" class="ml-2 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <span id="theme-icon" class="text-xl">🌙</span>
                </button>
            </div>
        </div>

        <div class="bg-blue-50 dark:bg-blue-900 border-l-4 border-blue-500 dark:border-blue-400 p-4 mb-6 rounded">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-500 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        Viewing database metadata for <span class="font-semibold">{{ filename }}</span> in category <span class="font-semibold">{{ category }}</span>
                    </p>
                </div>
            </div>
        </div>

        {% if metadata %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Basic Information</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Database ID:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.id }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">System Filename:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.filename }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Original Filename:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.original_filename }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Category:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.category }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Created At:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.created_at }}</span>
                        </div>
                    </div>
                </div>

                <!-- File Properties -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">File Properties</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">File Size:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">
                                {% if metadata.file_size %}
                                    {{ "%.2f"|format(metadata.file_size / 1024 / 1024) }} MB
                                {% else %}
                                    Unknown
                                {% endif %}
                            </span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Page Count:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">
                                {% if metadata.page_count %}
                                    {{ metadata.page_count }} pages
                                {% else %}
                                    Unknown
                                {% endif %}
                            </span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">File Hash (SHA-256):</span>
                            <div class="mt-1">
                                {% if metadata.file_hash %}
                                    <code class="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-gray-800 dark:text-gray-200 break-all">{{ metadata.file_hash }}</code>
                                {% else %}
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Not available</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Access Control -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Access Control</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Access Type:</span>
                            {% if metadata.is_gated %}
                                <span class="ml-2 px-2 py-1 bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs font-semibold rounded-full">
                                    Gated (Form Required)
                                </span>
                            {% else %}
                                <span class="ml-2 px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs font-semibold rounded-full">
                                    Public Access
                                </span>
                            {% endif %}
                        </div>
                        {% if metadata.form_id %}
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Form ID:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.form_id }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Cover Image -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <h3 class="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Cover Image</h3>
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Status:</span>
                            {% if metadata.has_cover_image %}
                                <span class="ml-2 px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs font-semibold rounded-full">
                                    Available
                                </span>
                            {% else %}
                                <span class="ml-2 px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 text-xs font-semibold rounded-full">
                                    Not Available
                                </span>
                            {% endif %}
                        </div>
                        {% if metadata.cover_image_format %}
                        <div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">Format:</span>
                            <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">{{ metadata.cover_image_format }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 flex space-x-4">
                <a href="{{ url_for('serve_file', category=category, filename=metadata.filename) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Download File
                </a>
                {% if metadata.is_gated %}
                <a href="{{ url_for('download_gated_pdf', filename=metadata.filename) }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Gated Download
                </a>
                {% endif %}
            </div>
        {% else %}
            <div class="bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 dark:border-yellow-500 p-4 rounded">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700 dark:text-yellow-300">
                            No metadata found for this file. This might indicate an issue with the database record.
                        </p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block scripts %}
    <script>
        // Initialize theme and functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();

            // Add theme toggle button event listener
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');
                    DMSUtils.toggleDarkMode(!isDarkMode);
                });
            }

            // Set initial theme icon based on current theme
            const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                              document.documentElement.classList.contains('dark');
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = isDarkMode ? '☀️' : '🌙';
            }
        });
    </script>
{% endblock %}
