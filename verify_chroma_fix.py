#!/usr/bin/env python3
"""
Simple verification script to check ChromaDB manager implementation.
This script verifies the code structure without requiring the full Flask environment.
"""

import os
import ast
import sys

def check_file_exists(filepath):
    """Check if a file exists and return its status."""
    if os.path.exists(filepath):
        print(f"✓ {filepath} exists")
        return True
    else:
        print(f"✗ {filepath} missing")
        return False

def check_imports_in_file(filepath, expected_imports):
    """Check if expected imports are present in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        
        for expected in expected_imports:
            if any(expected in imp for imp in imports):
                print(f"  ✓ {expected} import found")
            else:
                print(f"  ✗ {expected} import missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ✗ Error checking imports in {filepath}: {e}")
        return False

def check_function_exists(filepath, function_name):
    """Check if a function exists in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == function_name:
                print(f"  ✓ Function {function_name} found")
                return True
        
        print(f"  ✗ Function {function_name} not found")
        return False
        
    except Exception as e:
        print(f"  ✗ Error checking function in {filepath}: {e}")
        return False

def check_class_exists(filepath, class_name):
    """Check if a class exists in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                print(f"  ✓ Class {class_name} found")
                return True
        
        print(f"  ✗ Class {class_name} not found")
        return False
        
    except Exception as e:
        print(f"  ✗ Error checking class in {filepath}: {e}")
        return False

def main():
    """Run verification checks."""
    print("ChromaDB Manager Implementation Verification")
    print("=" * 50)
    
    all_checks_passed = True
    
    # Check if new ChromaDB manager file exists
    print("\n1. Checking ChromaDB Manager File:")
    chroma_manager_path = "app/services/chroma_manager.py"
    if check_file_exists(chroma_manager_path):
        # Check for key components
        if not check_class_exists(chroma_manager_path, "ChromaDBManager"):
            all_checks_passed = False
        if not check_function_exists(chroma_manager_path, "get_chroma_manager"):
            all_checks_passed = False
        if not check_function_exists(chroma_manager_path, "get_unified_chroma_db"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check vector_db.py updates
    print("\n2. Checking vector_db.py Updates:")
    vector_db_path = "app/services/vector_db.py"
    if check_file_exists(vector_db_path):
        expected_imports = ["chroma_manager"]
        if not check_imports_in_file(vector_db_path, expected_imports):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check optimized_vector_db.py updates
    print("\n3. Checking optimized_vector_db.py Updates:")
    optimized_db_path = "app/services/optimized_vector_db.py"
    if check_file_exists(optimized_db_path):
        expected_imports = ["chroma_manager"]
        if not check_imports_in_file(optimized_db_path, expected_imports):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check unified_vector_db.py updates
    print("\n4. Checking unified_vector_db.py Updates:")
    unified_db_path = "app/services/unified_vector_db.py"
    if check_file_exists(unified_db_path):
        expected_imports = ["chroma_manager"]
        if not check_imports_in_file(unified_db_path, expected_imports):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check documentation
    print("\n5. Checking Documentation:")
    doc_path = "docs/CHROMADB_CONFLICT_RESOLUTION.md"
    if not check_file_exists(doc_path):
        all_checks_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 All verification checks passed!")
        print("\nNext steps:")
        print("1. Test the solution in your development environment")
        print("2. Monitor for ChromaDB instance conflicts")
        print("3. Gradually migrate code to use the centralized manager")
        print("4. Update team documentation")
    else:
        print("❌ Some verification checks failed!")
        print("Please review the issues above and fix them before proceeding.")
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
