import sqlite3

print("Testing database-centric implementation...")

try:
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    # Check schema
    cursor.execute("PRAGMA table_info(pdf_documents)")
    columns = cursor.fetchall()
    
    required_columns = ['file_hash', 'pdf_content_blob', 'cover_image_blob', 'cover_image_format']
    existing_columns = [col[1] for col in columns]
    
    print("Schema check:")
    for col in required_columns:
        if col in existing_columns:
            print(f"  ✅ {col}")
        else:
            print(f"  ❌ {col} missing")
    
    # Check data
    cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE pdf_content_blob IS NOT NULL')
    blob_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM pdf_documents')
    total_count = cursor.fetchone()[0]
    
    print(f"\nData check:")
    print(f"  Total records: {total_count}")
    print(f"  Records with PDF content: {blob_count}")
    
    if blob_count == total_count and total_count > 0:
        print("\n✅ Database-centric implementation is working!")
    else:
        print("\n❌ Database-centric implementation has issues")
    
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")
