#!/usr/bin/env python3
"""
Test script to verify duplicate detection is working correctly.
This script will:
1. Create a test PDF file
2. Upload it twice to test duplicate detection
3. Show detailed logging of the duplicate detection process
"""

import os
import sys
import sqlite3
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Import the functions we need to test
from app.utils.content_db import get_pdf_by_original_filename

def create_test_database_record():
    """Create a test database record to simulate an uploaded file"""
    test_filename = "test_document.pdf"
    test_category = "CANOPY"
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    timestamped_filename = f"{timestamp}_{test_filename}"

    try:
        from app.utils.content_db import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Insert a test record
            cursor.execute('''
                INSERT INTO pdf_documents (filename, original_filename, category, upload_date, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                timestamped_filename,
                test_filename,
                test_category,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))

            record_id = cursor.lastrowid
            conn.commit()

            print(f"✅ Created test record: ID={record_id}")
            print(f"   Filename: {timestamped_filename}")
            print(f"   Original filename: {test_filename}")
            print(f"   Category: {test_category}")

            return record_id, timestamped_filename, test_filename, test_category

    except Exception as e:
        print(f"❌ Error creating test record: {e}")
        return None, None, None, None

def test_duplicate_detection():
    """Test the duplicate detection functionality"""
    print("🧪 Testing Duplicate Detection System")
    print("=" * 50)

    # Test parameters
    test_filename = "test_document.pdf"
    test_category = "CANOPY"

    print(f"📄 Test file: {test_filename}")
    print(f"📁 Category: {test_category}")
    print()

    # Clean up any existing test records
    print("🧹 Cleaning up existing test records...")
    try:
        from app.utils.content_db import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                DELETE FROM pdf_documents
                WHERE original_filename = ? AND category = ?
            ''', (test_filename, test_category))
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"   Deleted {deleted_count} existing records")
    except Exception as e:
        print(f"   Error cleaning up: {e}")
    print()

    # Test 1: Create a test database record
    print("📤 Test 1: Creating test database record")
    print("-" * 30)

    record_id, timestamped_filename, original_filename, category = create_test_database_record()
    if not record_id:
        print("❌ Failed to create test record, aborting test")
        return
    print()

    # Test 2: Test duplicate detection query
    print("🔍 Test 2: Testing duplicate detection query")
    print("-" * 30)

    result = get_pdf_by_original_filename(test_filename, test_category)
    if result:
        print("   ✅ Direct query found record:")
        print(f"   ID: {result.get('id')}")
        print(f"   Filename: {result.get('filename')}")
        print(f"   Original filename: {result.get('original_filename')}")
        print(f"   Category: {result.get('category')}")
    else:
        print("   ❌ Direct query found no record")
    print()
    
    # Test 3: Check all database records
    print("🗄️  Test 3: Database state verification")
    print("-" * 30)

    try:
        from app.utils.content_db import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at
                FROM pdf_documents
                WHERE original_filename = ? AND category = ?
                ORDER BY created_at DESC
            ''', (test_filename, test_category))
            rows = cursor.fetchall()

            print(f"   Records found: {len(rows)}")
            for i, row in enumerate(rows):
                print(f"   Record {i+1}: ID={row[0]}, filename='{row[1]}', original_filename='{row[2]}', category='{row[3]}', created_at='{row[4]}'")
    except Exception as e:
        print(f"   Error querying database: {e}")
    print()

    print("🏁 Test completed!")
    print("=" * 50)

if __name__ == "__main__":
    test_duplicate_detection()
