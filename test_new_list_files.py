#!/usr/bin/env python3
"""
Test the new database-first list_files logic
"""

import os
import sqlite3
import logging

logger = logging.getLogger(__name__)

def get_database_files():
    """Get all PDF files from the database"""
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')
        
        records = cursor.fetchall()
        conn.close()
        
        # Group by category
        files_by_category = {}
        for record in records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record
            
            if category not in files_by_category:
                files_by_category[category] = []
            
            files_by_category[category].append({
                'id': pdf_id,
                'filename': filename,
                'original_filename': original_filename,
                'category': category,
                'created_at': created_at,
                'file_size': file_size,
                'page_count': page_count
            })
        
        return files_by_category
        
    except Exception as e:
        logger.error(f"Error getting database files: {e}")
        return {}

def find_filesystem_path(filename, category):
    """Find the actual filesystem path for a database file"""
    TEMP_FOLDER = 'data'
    
    # Possible paths to check
    possible_paths = [
        # New structure with subdirectories
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
        
        # Legacy structure - direct files
        os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, category, filename),
        os.path.join(TEMP_FOLDER, '_temp', category, f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, '_temp', category, f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, '_temp', category, filename),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_new_list_files():
    """Test the new database-first approach"""
    print("🧪 Testing Database-First List Files")
    print("=" * 50)
    
    # Get files from database
    db_files = get_database_files()
    print(f"Found {sum(len(files) for files in db_files.values())} files in database")
    
    files_data = {}
    
    # Process each category
    for category, db_file_list in db_files.items():
        print(f"\n📁 Processing category: {category}")
        files = []
        
        for db_file in db_file_list:
            filename = db_file['filename']
            original_filename = db_file['original_filename']
            
            print(f"  📄 Processing: {original_filename}")
            print(f"     DB filename: {filename}")
            
            # Find filesystem path
            fs_path = find_filesystem_path(filename, category)
            
            if fs_path:
                print(f"     ✅ Found filesystem path: {fs_path}")
                
                # Create file data structure
                file_data = {
                    "original_filename": original_filename,
                    "source": os.path.basename(fs_path),
                    "type": "pdf",
                    "database_id": db_file['id'],
                    "created_at": db_file['created_at'],
                    "file_size": db_file['file_size'],
                    "page_count": db_file['page_count']
                }
                
                files.append(file_data)
            else:
                print(f"     ❌ Filesystem path not found")
                # Still add to list but mark as missing
                file_data = {
                    "original_filename": original_filename,
                    "source": filename,
                    "type": "pdf",
                    "database_id": db_file['id'],
                    "created_at": db_file['created_at'],
                    "file_size": db_file['file_size'],
                    "page_count": db_file['page_count'],
                    "filesystem_missing": True
                }
                files.append(file_data)
        
        if files:
            files_data[category] = files
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"Categories: {len(files_data)}")
    
    for category, files in files_data.items():
        valid_files = [f for f in files if not f.get('filesystem_missing')]
        missing_files = [f for f in files if f.get('filesystem_missing')]
        
        print(f"  {category}: {len(files)} total ({len(valid_files)} valid, {len(missing_files)} missing)")
        
        for file_info in files:
            status = "❌ MISSING" if file_info.get('filesystem_missing') else "✅ FOUND"
            print(f"    {status} {file_info['original_filename']}")
    
    return files_data

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_new_list_files()
