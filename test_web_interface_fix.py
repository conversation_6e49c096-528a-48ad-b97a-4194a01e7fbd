#!/usr/bin/env python3
"""
Test script to verify the web interface fix shows correct number of files
"""

import os
import sys
import sqlite3

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def get_db_connection():
    """Get database connection"""
    return sqlite3.connect('erdb_main.db')

def simulate_old_filesystem_approach():
    """Simulate the old filesystem-based approach"""
    print("🔍 OLD APPROACH (Filesystem-based):")
    print("-" * 40)
    
    TEMP_FOLDER = 'data'
    category = 'CANOPY'
    files_found = []
    
    # Check both possible category paths (like the old code)
    category_paths = [
        os.path.join(TEMP_FOLDER, category),
        os.path.join(TEMP_FOLDER, "_temp", category),
        os.path.join(TEMP_FOLDER, "temp", category)
    ]
    
    for category_path in category_paths:
        if os.path.isdir(category_path):
            print(f"  Checking: {category_path}")
            
            # Check for direct PDF files
            for item in os.listdir(category_path):
                item_path = os.path.join(category_path, item)
                
                if os.path.isfile(item_path) and item.endswith('.pdf'):
                    files_found.append(item)
                elif os.path.isdir(item_path):
                    # Check subdirectories for PDF files
                    for subitem in os.listdir(item_path):
                        if subitem.endswith('.pdf'):
                            files_found.append(subitem)
    
    print(f"  Files found: {len(files_found)}")
    for file in files_found:
        print(f"    - {file}")
    
    return files_found

def simulate_new_database_approach():
    """Simulate the new database-first approach"""
    print("\n🔍 NEW APPROACH (Database-first):")
    print("-" * 40)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at
            FROM pdf_documents
            WHERE category = 'CANOPY'
            ORDER BY created_at DESC
        ''')
        
        db_records = cursor.fetchall()
        conn.close()
        
        print(f"  Database records: {len(db_records)}")
        
        valid_files = []
        for record in db_records:
            pdf_id, filename, original_filename, category, created_at = record
            print(f"    DB Record: {original_filename}")
            print(f"      filename: {filename}")
            print(f"      created_at: {created_at}")
            
            # Check if filesystem file exists
            TEMP_FOLDER = 'data'
            possible_paths = [
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, filename),
            ]
            
            fs_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    fs_path = path
                    break
            
            if fs_path:
                valid_files.append({
                    'original_filename': original_filename,
                    'filesystem_path': fs_path,
                    'database_id': pdf_id
                })
                print(f"      ✅ Found filesystem file: {fs_path}")
            else:
                print(f"      ❌ No filesystem file found")
        
        print(f"\n  Valid files (with both DB record and filesystem file): {len(valid_files)}")
        for file in valid_files:
            print(f"    - {file['original_filename']}")
        
        return valid_files
        
    except Exception as e:
        print(f"  Error: {e}")
        return []

def main():
    print("🧪 WEB INTERFACE FIX VERIFICATION")
    print("=" * 60)
    
    # Test old approach
    old_files = simulate_old_filesystem_approach()
    
    # Test new approach
    new_files = simulate_new_database_approach()
    
    # Compare results
    print(f"\n📊 COMPARISON:")
    print("=" * 60)
    print(f"Old approach (filesystem): {len(old_files)} files")
    print(f"New approach (database):   {len(new_files)} files")
    
    if len(new_files) < len(old_files):
        print(f"✅ SUCCESS: New approach shows {len(old_files) - len(new_files)} fewer files")
        print("   This means duplicates are no longer shown in the web interface!")
    elif len(new_files) == len(old_files):
        print("⚠️  SAME: Both approaches show the same number of files")
    else:
        print("❌ ISSUE: New approach shows more files than old approach")
    
    print(f"\n🎯 EXPECTED RESULT:")
    print("   - Old approach: 6 files (including duplicates)")
    print("   - New approach: 2 files (only valid database records)")
    print("   - Web interface should now show only 2 files instead of 6")
    
    if len(new_files) == 2:
        print("\n🎉 PERFECT! The fix is working correctly!")
        print("   The web interface will now show only 2 CANOPY_INTERNATIONAL files")
        print("   instead of the previous 6 duplicate files.")
    else:
        print(f"\n⚠️  The new approach shows {len(new_files)} files instead of expected 2")

if __name__ == "__main__":
    main()
