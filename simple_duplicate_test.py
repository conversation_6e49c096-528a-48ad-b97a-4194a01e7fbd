#!/usr/bin/env python3

"""
Simple test to verify duplicate detection fixes
"""

import sqlite3
import hashlib
from datetime import datetime

def test_database_schema():
    """Test database schema"""
    print("Testing database schema...")
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Check if upload_date column exists
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        if 'upload_date' in column_names:
            print("✅ upload_date column exists")
        else:
            print("❌ upload_date column missing")
            
        if 'original_filename' in column_names:
            print("✅ original_filename column exists")
        else:
            print("❌ original_filename column missing")
            
        if 'file_hash' in column_names:
            print("✅ file_hash column exists")
        else:
            print("❌ file_hash column missing")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_simple_insert():
    """Test simple database insert with upload_date"""
    print("\nTesting database insert...")
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Test insert with upload_date
        current_time = datetime.now()
        test_data = (
            'test_system_filename.pdf',
            'test_original_filename.pdf', 
            'TEST',
            1000,  # file_size
            1,     # page_count
            b'test content',  # pdf_content_blob
            None,  # cover_image_blob
            None,  # cover_image_format
            'test_hash',  # file_hash
            None,  # form_id
            current_time,  # upload_date
            current_time   # created_at
        )
        
        cursor.execute('''
            INSERT INTO pdf_documents (
                filename, original_filename, category, file_size, page_count,
                pdf_content_blob, cover_image_blob, cover_image_format,
                file_hash, form_id, upload_date, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_data)
        
        pdf_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ Insert successful, PDF ID: {pdf_id}")
        
        # Clean up
        cursor.execute("DELETE FROM pdf_documents WHERE id = ?", (pdf_id,))
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Insert test failed: {e}")
        return False

def test_duplicate_detection_logic():
    """Test the duplicate detection logic"""
    print("\nTesting duplicate detection logic...")
    
    try:
        # Test 1: Insert first file
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        current_time = datetime.now()
        
        # Insert first file
        cursor.execute('''
            INSERT INTO pdf_documents (
                filename, original_filename, category, file_size, page_count,
                pdf_content_blob, file_hash, upload_date, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '20250717120000_DUPLICATE_TEST.pdf',  # Different system filename
            'DUPLICATE_TEST.pdf',                 # Same original filename
            'TEST',
            1000,
            1,
            b'test content 1',
            'hash1',
            current_time,
            current_time
        ))
        
        first_id = cursor.lastrowid
        
        # Insert second file with same original filename but different system filename
        cursor.execute('''
            INSERT INTO pdf_documents (
                filename, original_filename, category, file_size, page_count,
                pdf_content_blob, file_hash, upload_date, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '20250717130000_DUPLICATE_TEST.pdf',  # Different system filename
            'DUPLICATE_TEST.pdf',                 # Same original filename
            'TEST',
            1000,
            1,
            b'test content 2',
            'hash2',
            current_time,
            current_time
        ))
        
        second_id = cursor.lastrowid
        conn.commit()
        
        # Test duplicate detection by original filename
        cursor.execute('''
            SELECT id, filename, original_filename
            FROM pdf_documents 
            WHERE original_filename = ? AND category = ?
        ''', ('DUPLICATE_TEST.pdf', 'TEST'))
        
        duplicates = cursor.fetchall()
        
        if len(duplicates) >= 2:
            print(f"✅ Found {len(duplicates)} files with same original filename:")
            for dup in duplicates:
                print(f"   ID: {dup[0]}, System: {dup[1]}, Original: {dup[2]}")
        else:
            print(f"❌ Expected 2+ duplicates, found {len(duplicates)}")
        
        # Clean up
        cursor.execute("DELETE FROM pdf_documents WHERE id IN (?, ?)", (first_id, second_id))
        conn.commit()
        conn.close()
        
        return len(duplicates) >= 2
        
    except Exception as e:
        print(f"❌ Duplicate detection test failed: {e}")
        return False

def main():
    print("🚀 Simple Duplicate Detection Test")
    print("=" * 40)
    
    schema_ok = test_database_schema()
    insert_ok = test_simple_insert()
    duplicate_ok = test_duplicate_detection_logic()
    
    print("\n" + "=" * 40)
    print("SUMMARY:")
    if schema_ok:
        print("✅ Database schema is correct")
    else:
        print("❌ Database schema has issues")
        
    if insert_ok:
        print("✅ Database insert with upload_date works")
    else:
        print("❌ Database insert fails")
        
    if duplicate_ok:
        print("✅ Duplicate detection by original filename works")
    else:
        print("❌ Duplicate detection has issues")
    
    if schema_ok and insert_ok and duplicate_ok:
        print("\n🎉 All tests passed! Fixes are working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
