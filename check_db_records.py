#!/usr/bin/env python3
import sqlite3

conn = sqlite3.connect('erdb_main.db')
cursor = conn.cursor()

print("📊 All database records:")
cursor.execute('SELECT id, filename, original_filename, category, created_at FROM pdf_documents ORDER BY created_at DESC')
rows = cursor.fetchall()

for row in rows:
    print(f"ID={row[0]}, filename={row[1]}")
    print(f"  original_filename={row[2]}")
    print(f"  category={row[3]}, created_at={row[4]}")
    print()

print(f"Total records: {len(rows)}")

# Check specifically for CANOPY_INTERNATIONAL
print("\n🔍 CANOPY_INTERNATIONAL records:")
cursor.execute("SELECT id, filename, original_filename, category, created_at FROM pdf_documents WHERE original_filename LIKE '%CANOPY%INTERNATIONAL%' ORDER BY created_at DESC")
canopy_rows = cursor.fetchall()

for row in canopy_rows:
    print(f"ID={row[0]}, filename={row[1]}")
    print(f"  original_filename={row[2]}")
    print(f"  category={row[3]}, created_at={row[4]}")
    print()

print(f"CANOPY_INTERNATIONAL records: {len(canopy_rows)}")

conn.close()
