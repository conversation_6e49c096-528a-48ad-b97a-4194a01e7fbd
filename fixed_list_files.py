#!/usr/bin/env python3
"""
Fixed list_files() function that uses database as primary source
"""

import os
import sqlite3
from flask import render_template, flash, redirect, url_for
import logging

logger = logging.getLogger(__name__)

def get_database_files():
    """Get all PDF files from the database"""
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')
        
        records = cursor.fetchall()
        conn.close()
        
        # Group by category
        files_by_category = {}
        for record in records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record
            
            if category not in files_by_category:
                files_by_category[category] = []
            
            files_by_category[category].append({
                'id': pdf_id,
                'filename': filename,
                'original_filename': original_filename,
                'category': category,
                'created_at': created_at,
                'file_size': file_size,
                'page_count': page_count
            })
        
        return files_by_category
        
    except Exception as e:
        logger.error(f"Error getting database files: {e}")
        return {}

def find_filesystem_path(filename, category):
    """Find the actual filesystem path for a database file"""
    TEMP_FOLDER = 'data'
    
    # Possible paths to check
    possible_paths = [
        # New structure with subdirectories
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
        
        # Legacy structure - direct files
        os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, category, filename),
        os.path.join(TEMP_FOLDER, '_temp', category, f'non_ocr_{filename}'),
        os.path.join(TEMP_FOLDER, '_temp', category, f'ocr_{filename}'),
        os.path.join(TEMP_FOLDER, '_temp', category, filename),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def get_vector_metadata(category, filename):
    """Get metadata from vector database"""
    try:
        from app.utils.helpers import get_vector_db
        
        db = get_vector_db(category)
        
        # Try different filename variations for vector search
        search_filenames = [
            filename,
            f'non_ocr_{filename}',
            f'ocr_{filename}'
        ]
        
        for search_filename in search_filenames:
            docs = db.similarity_search_with_score("", k=1, filter={"source": search_filename})
            if docs and len(docs) > 0:
                doc, _ = docs[0]
                return {
                    'source': search_filename,
                    'original_url': doc.metadata.get("original_url"),
                    'source_url_id': doc.metadata.get("source_url_id"),
                    'pdf_document_id': doc.metadata.get("pdf_document_id"),
                    'cover_image_id': doc.metadata.get("cover_image_id"),
                    'database_retrieval': doc.metadata.get("database_retrieval", False)
                }
        
        return None
        
    except Exception as e:
        logger.warning(f"Could not get vector metadata for {category}/{filename}: {e}")
        return None

def list_files_database_first():
    """
    New list_files function that uses database as primary source
    """
    try:
        files_data = {}
        
        # Get files from database
        db_files = get_database_files()
        logger.info(f"Found {sum(len(files) for files in db_files.values())} files in database")
        
        # Process each category
        for category, db_file_list in db_files.items():
            files = []
            
            for db_file in db_file_list:
                filename = db_file['filename']
                original_filename = db_file['original_filename']
                
                # Find filesystem path
                fs_path = find_filesystem_path(filename, category)
                
                if fs_path:
                    # Get vector metadata
                    vector_metadata = get_vector_metadata(category, filename)
                    
                    # Create file data structure
                    file_data = {
                        "original_filename": original_filename,
                        "source": os.path.basename(fs_path),  # Use actual filesystem filename
                        "type": "pdf",
                        "database_id": db_file['id'],
                        "created_at": db_file['created_at'],
                        "file_size": db_file['file_size'],
                        "page_count": db_file['page_count']
                    }
                    
                    # Add vector metadata if available
                    if vector_metadata:
                        if vector_metadata.get('original_url'):
                            file_data["original_url"] = vector_metadata['original_url']
                        if vector_metadata.get('source_url_id'):
                            file_data["source_url_id"] = vector_metadata['source_url_id']
                        if vector_metadata.get('pdf_document_id'):
                            file_data["pdf_document_id"] = vector_metadata['pdf_document_id']
                        if vector_metadata.get('cover_image_id'):
                            file_data["cover_image_id"] = vector_metadata['cover_image_id']
                        if vector_metadata.get('database_retrieval'):
                            file_data["database_retrieval"] = vector_metadata['database_retrieval']
                    
                    files.append(file_data)
                    logger.debug(f"Added file: {category}/{original_filename}")
                else:
                    logger.warning(f"Database file not found in filesystem: {category}/{filename}")
                    # Still add to list but mark as missing
                    file_data = {
                        "original_filename": original_filename,
                        "source": filename,
                        "type": "pdf",
                        "database_id": db_file['id'],
                        "created_at": db_file['created_at'],
                        "file_size": db_file['file_size'],
                        "page_count": db_file['page_count'],
                        "filesystem_missing": True
                    }
                    files.append(file_data)
            
            if files:
                files_data[category] = files
        
        # Add URL files from scraped pages (existing logic)
        try:
            from app.utils import db_utils
            scraped_pages = db_utils.get_scraped_pages()
            
            for page in scraped_pages:
                category = page['category']
                if category not in files_data:
                    files_data[category] = []
                
                url_file_data = {
                    "original_filename": page['url'],
                    "source": page['filename'],
                    "type": "url",
                    "scrape_depth": page.get('scrape_depth', 0),
                    "pages_scraped": page.get('pages_scraped', 0)
                }
                files_data[category].append(url_file_data)
                
        except Exception as e:
            logger.warning(f"Could not load scraped pages: {e}")
        
        logger.info(f"Final files_data: {len(files_data)} categories")
        return files_data
        
    except Exception as e:
        logger.error(f"Error in list_files_database_first: {e}")
        return {}

# Test function
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("🧪 Testing new list_files function")
    print("=" * 50)
    
    files_data = list_files_database_first()
    
    for category, files in files_data.items():
        print(f"\n📁 {category} ({len(files)} files):")
        for file_info in files:
            print(f"  📄 {file_info['original_filename']}")
            print(f"     Source: {file_info['source']}")
            print(f"     Type: {file_info['type']}")
            if file_info.get('filesystem_missing'):
                print(f"     ⚠️  WARNING: File missing from filesystem")
            if file_info.get('database_id'):
                print(f"     DB ID: {file_info['database_id']}")
            print()
