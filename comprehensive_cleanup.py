#!/usr/bin/env python3
"""
Comprehensive cleanup script to remove duplicate files and synchronize database
"""

import os
import sqlite3
import shutil
from datetime import datetime
import hashlib

def get_file_hash(filepath):
    """Calculate MD5 hash of a file"""
    hash_md5 = hashlib.md5()
    try:
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {filepath}: {e}")
        return None

def analyze_duplicates():
    """Analyze duplicate files and database records"""
    
    print('=== ANALYZING DUPLICATES ===')
    
    # Get database records
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, filename, original_filename, category, created_at
        FROM pdf_documents
        ORDER BY original_filename, created_at DESC
    ''')
    db_records = cursor.fetchall()
    conn.close()
    
    print(f'Database records: {len(db_records)}')
    
    # Group by original filename
    by_original = {}
    for record in db_records:
        pdf_id, filename, original_filename, category, created_at = record
        if original_filename not in by_original:
            by_original[original_filename] = []
        by_original[original_filename].append({
            'id': pdf_id,
            'filename': filename,
            'original_filename': original_filename,
            'category': category,
            'created_at': created_at
        })
    
    # Find filesystem files
    TEMP_FOLDER = 'data'
    filesystem_files = []
    
    for root, dirs, files in os.walk(TEMP_FOLDER):
        for file in files:
            if file.endswith('.pdf'):
                full_path = os.path.join(root, file)
                size = os.path.getsize(full_path)
                file_hash = get_file_hash(full_path)
                filesystem_files.append({
                    'path': full_path,
                    'filename': file,
                    'size': size,
                    'hash': file_hash
                })
    
    print(f'Filesystem files: {len(filesystem_files)}')
    
    # Analyze duplicates
    print('\n=== DUPLICATE ANALYSIS ===')
    
    for original_filename, records in by_original.items():
        if len(records) > 1:
            print(f'\nDuplicate: {original_filename} ({len(records)} database records)')
            
            # Find corresponding filesystem files
            fs_files = []
            for record in records:
                filename = record['filename']
                category = record['category']
                
                # Look for filesystem file
                possible_paths = [
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        size = os.path.getsize(path)
                        file_hash = get_file_hash(path)
                        fs_files.append({
                            'record': record,
                            'path': path,
                            'size': size,
                            'hash': file_hash
                        })
                        break
            
            print(f'  Database records: {len(records)}')
            print(f'  Filesystem files: {len(fs_files)}')
            
            # Show details
            for i, record in enumerate(records):
                print(f'    DB {i+1}: ID={record["id"]}, File={record["filename"]}, Created={record["created_at"]}')
            
            for i, fs_file in enumerate(fs_files):
                print(f'    FS {i+1}: {fs_file["path"]} (Size: {fs_file["size"]}, Hash: {fs_file["hash"][:8]}...)')
    
    return by_original, filesystem_files

def cleanup_duplicates(by_original, filesystem_files, dry_run=True):
    """Clean up duplicate files and database records"""
    
    print(f'\n=== CLEANUP (DRY RUN: {dry_run}) ===')
    
    files_to_delete = []
    db_records_to_delete = []
    total_size_freed = 0
    
    for original_filename, records in by_original.items():
        if len(records) > 1:
            print(f'\nCleaning up: {original_filename}')
            
            # Keep the most recent record (first in list since sorted by created_at DESC)
            keep_record = records[0]
            delete_records = records[1:]
            
            print(f'  KEEP: ID={keep_record["id"]}, File={keep_record["filename"]}, Created={keep_record["created_at"]}')
            
            for record in delete_records:
                print(f'  DELETE DB: ID={record["id"]}, File={record["filename"]}, Created={record["created_at"]}')
                db_records_to_delete.append(record['id'])
                
                # Find and mark corresponding filesystem file for deletion
                filename = record['filename']
                category = record['category']
                
                possible_paths = [
                    os.path.join('data', 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                    os.path.join('data', 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                    os.path.join('data', 'temp', category, filename.replace('.pdf', ''), filename),
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        size = os.path.getsize(path)
                        total_size_freed += size
                        files_to_delete.append(path)
                        print(f'    DELETE FS: {path} ({size} bytes)')
                        
                        # Also delete the parent directory if it becomes empty
                        parent_dir = os.path.dirname(path)
                        files_to_delete.append(parent_dir)
                        break
    
    print(f'\n=== CLEANUP SUMMARY ===')
    print(f'Database records to delete: {len(db_records_to_delete)}')
    print(f'Filesystem files to delete: {len([f for f in files_to_delete if f.endswith(".pdf")])}')
    print(f'Directories to delete: {len([f for f in files_to_delete if not f.endswith(".pdf")])}')
    print(f'Total size to free: {total_size_freed / (1024*1024):.2f} MB')
    
    if not dry_run:
        print('\n=== EXECUTING CLEANUP ===')
        
        # Delete database records
        if db_records_to_delete:
            conn = sqlite3.connect('erdb_main.db')
            cursor = conn.cursor()
            
            for record_id in db_records_to_delete:
                cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (record_id,))
                print(f'Deleted database record ID: {record_id}')
            
            conn.commit()
            conn.close()
        
        # Delete filesystem files and directories
        for item in files_to_delete:
            try:
                if os.path.exists(item):
                    if os.path.isfile(item):
                        os.remove(item)
                        print(f'Deleted file: {item}')
                    elif os.path.isdir(item) and not os.listdir(item):  # Only delete if empty
                        os.rmdir(item)
                        print(f'Deleted empty directory: {item}')
            except Exception as e:
                print(f'Error deleting {item}: {e}')
        
        print('\n✅ CLEANUP COMPLETED!')
    else:
        print('\n⚠️  DRY RUN - No changes made. Run with dry_run=False to execute.')
    
    return len(db_records_to_delete), len([f for f in files_to_delete if f.endswith(".pdf")]), total_size_freed

def main():
    """Main cleanup function"""
    
    print('🧹 COMPREHENSIVE DUPLICATE CLEANUP')
    print('=' * 50)
    
    # Analyze duplicates
    by_original, filesystem_files = analyze_duplicates()
    
    # Run cleanup in dry-run mode first
    db_deletes, file_deletes, size_freed = cleanup_duplicates(by_original, filesystem_files, dry_run=True)
    
    if db_deletes > 0 or file_deletes > 0:
        print('\n' + '=' * 50)
        response = input('Execute cleanup? (y/N): ').strip().lower()
        
        if response == 'y':
            cleanup_duplicates(by_original, filesystem_files, dry_run=False)
        else:
            print('Cleanup cancelled.')
    else:
        print('\n✅ No duplicates found - system is clean!')

if __name__ == '__main__':
    main()
