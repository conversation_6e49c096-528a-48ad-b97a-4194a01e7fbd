#!/usr/bin/env python3
"""
Cleanup script to remove duplicate filesystem entries and synchronize with database
"""

import os
import sqlite3
import shutil
from datetime import datetime

def get_database_records():
    """Get all PDF records from the SQLite database"""
    print("📊 Getting database records...")
    
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, filename, original_filename, category, created_at
        FROM pdf_documents
        ORDER BY created_at DESC
    ''')
    
    records = cursor.fetchall()
    conn.close()
    
    print(f"Found {len(records)} database records")
    return records

def find_filesystem_files():
    """Find all PDF files in the filesystem"""
    print("📁 Scanning filesystem...")
    
    TEMP_FOLDER = 'data'
    filesystem_files = []
    
    # Check both possible category paths
    for category in ['CANOPY', 'RISE', 'MANUAL', 'ERDB INFOJOURNAL']:
        category_paths = [
            os.path.join(TEMP_FOLDER, category),
            os.path.join(TEMP_FOLDER, '_temp', category),
            os.path.join(TEMP_FOLDER, 'temp', category)
        ]
        
        for category_path in category_paths:
            if os.path.isdir(category_path):
                print(f"  Checking: {category_path}")
                
                # Check for direct PDF files
                for item in os.listdir(category_path):
                    item_path = os.path.join(category_path, item)
                    
                    if os.path.isfile(item_path) and item.endswith('.pdf'):
                        filesystem_files.append({
                            'path': item_path,
                            'filename': item,
                            'category': category,
                            'type': 'direct_file'
                        })
                    elif os.path.isdir(item_path):
                        # Check for PDF files in subdirectories
                        for subitem in os.listdir(item_path):
                            if subitem.endswith('.pdf'):
                                subitem_path = os.path.join(item_path, subitem)
                                filesystem_files.append({
                                    'path': subitem_path,
                                    'filename': subitem,
                                    'directory': item,
                                    'category': category,
                                    'type': 'subdirectory_file'
                                })
    
    print(f"Found {len(filesystem_files)} filesystem files")
    return filesystem_files

def identify_duplicates_and_orphans(db_records, fs_files):
    """Identify duplicate and orphaned files"""
    print("🔍 Identifying duplicates and orphans...")
    
    # Create mapping of database records
    db_filenames = {record[1]: record for record in db_records}  # filename -> record
    
    valid_files = []
    duplicate_files = []
    orphaned_files = []
    
    # Group filesystem files by original filename
    fs_groups = {}
    for fs_file in fs_files:
        # Extract original filename
        filename = fs_file['filename']
        if filename.startswith('non_ocr_'):
            # Remove 'non_ocr_' prefix and timestamp
            parts = filename.split('_')
            if len(parts) >= 3:
                original = '_'.join(parts[3:])  # Skip 'non', 'ocr', timestamp
            else:
                original = filename[8:]  # Fallback
        elif filename.startswith('ocr_'):
            # Remove 'ocr_' prefix and timestamp
            parts = filename.split('_')
            if len(parts) >= 2:
                original = '_'.join(parts[2:])  # Skip 'ocr', timestamp
            else:
                original = filename[4:]  # Fallback
        else:
            original = filename.split('_', 1)[1] if '_' in filename else filename
        
        if original not in fs_groups:
            fs_groups[original] = []
        fs_groups[original].append(fs_file)
    
    # Analyze each group
    for original_filename, fs_group in fs_groups.items():
        # Check if this file exists in database (try multiple variations)
        db_matches = []
        for record in db_records:
            db_original = record[2]
            # Try exact match
            if db_original == original_filename:
                db_matches.append(record)
            # Try with spaces vs underscores
            elif db_original.replace(' ', '_') == original_filename.replace(' ', '_'):
                db_matches.append(record)
            # Try converting filesystem underscores to spaces
            elif db_original == original_filename.replace('_', ' '):
                db_matches.append(record)
            # Try case insensitive
            elif db_original.lower() == original_filename.lower():
                db_matches.append(record)

        if not db_matches:
            # No database record - all filesystem files are orphaned
            orphaned_files.extend(fs_group)
            print(f"  ❌ ORPHANED: {original_filename} ({len(fs_group)} filesystem files, no database record)")
        elif len(fs_group) == 1:
            # Single filesystem file with database record - valid
            valid_files.extend(fs_group)
            print(f"  ✅ VALID: {original_filename}")
        else:
            # Multiple filesystem files - duplicates
            # Keep the one that matches the database filename timestamp, mark others as duplicates
            db_filename = db_matches[0][1]  # Get database filename
            db_timestamp = db_filename.split('_')[0] if '_' in db_filename else ''

            kept_file = None
            for fs_file in fs_group:
                fs_timestamp = fs_file['filename'].split('_')[1] if '_' in fs_file['filename'] else ''
                if db_timestamp and fs_timestamp and db_timestamp == fs_timestamp:
                    kept_file = fs_file
                    valid_files.append(fs_file)
                    break

            if kept_file:
                duplicates = [f for f in fs_group if f != kept_file]
                duplicate_files.extend(duplicates)
                print(f"  ⚠️  DUPLICATES: {original_filename} (keeping 1, removing {len(duplicates)})")
            else:
                # No exact match - keep the most recent one
                fs_group.sort(key=lambda x: x['filename'], reverse=True)
                valid_files.append(fs_group[0])
                duplicate_files.extend(fs_group[1:])
                print(f"  ⚠️  DUPLICATES: {original_filename} (keeping most recent, removing {len(fs_group)-1})")
    
    return valid_files, duplicate_files, orphaned_files

def cleanup_files(files_to_remove, dry_run=True):
    """Remove duplicate and orphaned files"""
    print(f"\n🧹 Cleanup {'(DRY RUN)' if dry_run else '(ACTUAL REMOVAL)'}:")
    print("-" * 50)
    
    if not files_to_remove:
        print("No files to remove")
        return
    
    total_size = 0
    directories_to_remove = set()
    
    for file_info in files_to_remove:
        file_path = file_info['path']
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            
            print(f"{'[DRY RUN] ' if dry_run else ''}Remove: {file_path} ({size:,} bytes)")
            
            if not dry_run:
                os.remove(file_path)
            
            # Check if parent directory should be removed
            parent_dir = os.path.dirname(file_path)
            if file_info['type'] == 'subdirectory_file':
                directories_to_remove.add(parent_dir)
    
    # Remove empty directories
    for dir_path in directories_to_remove:
        if os.path.exists(dir_path) and not os.listdir(dir_path):
            print(f"{'[DRY RUN] ' if dry_run else ''}Remove empty directory: {dir_path}")
            if not dry_run:
                shutil.rmtree(dir_path)
    
    print(f"\nTotal space to be freed: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")

def main():
    print("🔧 DUPLICATE CLEANUP TOOL")
    print("=" * 60)
    
    # Get data from both sources
    db_records = get_database_records()
    fs_files = find_filesystem_files()
    
    # Identify issues
    valid_files, duplicate_files, orphaned_files = identify_duplicates_and_orphans(db_records, fs_files)
    
    # Summary
    print(f"\n📊 ANALYSIS SUMMARY:")
    print(f"  ✅ Valid files: {len(valid_files)}")
    print(f"  ⚠️  Duplicate files: {len(duplicate_files)}")
    print(f"  ❌ Orphaned files: {len(orphaned_files)}")
    
    # Show files to be removed
    files_to_remove = duplicate_files + orphaned_files
    
    if files_to_remove:
        print(f"\n🗑️  FILES TO BE REMOVED ({len(files_to_remove)}):")
        for file_info in files_to_remove:
            print(f"  {file_info['path']}")
        
        # Dry run first
        cleanup_files(files_to_remove, dry_run=True)
        
        # Ask for confirmation
        print(f"\n⚠️  WARNING: This will permanently delete {len(files_to_remove)} files!")
        response = input("Do you want to proceed with the cleanup? (yes/no): ").lower().strip()
        
        if response == 'yes':
            cleanup_files(files_to_remove, dry_run=False)
            print("\n✅ Cleanup completed!")
        else:
            print("\n❌ Cleanup cancelled")
    else:
        print("\n✅ No cleanup needed - all files are properly synchronized!")

if __name__ == "__main__":
    main()
