#!/usr/bin/env python3

print("Testing imports...")

try:
    import sys
    print(f"Python version: {sys.version}")
    
    import sqlite3
    print("✅ sqlite3 imported successfully")
    
    from app.utils.database_centric_helpers import store_pdf_in_database
    print("✅ database_centric_helpers imported successfully")
    
    from app.utils.content_db import get_db_connection
    print("✅ content_db imported successfully")
    
    # Test database connection
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM pdf_documents")
    count = cursor.fetchone()[0]
    conn.close()
    print(f"✅ Database connection successful, found {count} PDF documents")
    
    print("✅ All imports and database connection successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
