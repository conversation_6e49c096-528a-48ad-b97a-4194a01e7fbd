#!/usr/bin/env python3
"""
Critical System Restoration Script for ERDB

This script fixes the broken state by:
1. Analyzing current duplicates across all three systems
2. Cleaning up duplicate files while preserving the most recent versions
3. Synchronizing database, filesystem, and vector storage
4. Restoring proper system functionality

CRITICAL: This script restores the system to working state, not implementing new features.
"""

import sys
import os
import sqlite3
import shutil
from datetime import datetime
from collections import defaultdict

# Add the app directory to the Python path
sys.path.append('.')

def analyze_current_state():
    """Analyze the current broken state of all three systems"""
    print("🔍 ANALYZING CURRENT BROKEN STATE")
    print("=" * 50)
    
    # 1. Database Analysis
    print("\n1. DATABASE ANALYSIS:")
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM pdf_documents')
    db_count = cursor.fetchone()[0]
    print(f"   Total database records: {db_count}")
    
    cursor.execute('''
        SELECT id, filename, original_filename, category, created_at 
        FROM pdf_documents 
        ORDER BY original_filename, created_at DESC
    ''')
    db_records = cursor.fetchall()
    conn.close()
    
    # Group by original filename to find duplicates
    db_duplicates = defaultdict(list)
    for record in db_records:
        original_filename = record[2]  # original_filename
        db_duplicates[original_filename].append(record)
    
    print("   Database duplicates:")
    for original_filename, records in db_duplicates.items():
        if len(records) > 1:
            print(f"     {original_filename}: {len(records)} records")
            for record in records:
                print(f"       ID: {record[0]}, File: {record[1]}, Created: {record[4]}")
    
    # 2. Filesystem Analysis
    print("\n2. FILESYSTEM ANALYSIS:")
    filesystem_files = []
    for root, dirs, files in os.walk('data'):
        for file in files:
            if file.endswith('.pdf'):
                full_path = os.path.join(root, file)
                filesystem_files.append(full_path)
    
    print(f"   Total filesystem files: {len(filesystem_files)}")
    
    # Group by original filename
    fs_duplicates = defaultdict(list)
    for file_path in filesystem_files:
        filename = os.path.basename(file_path)
        # Extract original filename
        if filename.startswith('non_ocr_') or filename.startswith('ocr_'):
            base_filename = filename[8:] if filename.startswith('non_ocr_') else filename[4:]
            original_filename = base_filename.split('_', 1)[1] if '_' in base_filename else base_filename
        else:
            original_filename = filename.split('_', 1)[1] if '_' in filename else filename
        
        fs_duplicates[original_filename].append(file_path)
    
    print("   Filesystem duplicates:")
    for original_filename, paths in fs_duplicates.items():
        if len(paths) > 1:
            print(f"     {original_filename}: {len(paths)} files")
            for path in paths:
                print(f"       {path}")
    
    # 3. Vector Database Analysis
    print("\n3. VECTOR DATABASE ANALYSIS:")
    try:
        from app.utils.helpers import get_vector_db
        
        category = 'CANOPY'
        db = get_vector_db(category)
        docs = db.similarity_search_with_score('', k=100)
        
        print(f"   Total vector documents: {len(docs)}")
        
        # Group by source filename
        vector_sources = defaultdict(int)
        for doc, score in docs:
            source = doc.metadata.get('source', 'unknown')
            vector_sources[source] += 1
        
        print("   Vector database sources:")
        for source, count in vector_sources.items():
            print(f"     {source}: {count} chunks")
        
        # Group by original filename
        vector_duplicates = defaultdict(list)
        for source in vector_sources.keys():
            if source.startswith('non_ocr_') or source.startswith('ocr_'):
                base_filename = source[8:] if source.startswith('non_ocr_') else source[4:]
                original_filename = base_filename.split('_', 1)[1] if '_' in base_filename else base_filename
            else:
                original_filename = source.split('_', 1)[1] if '_' in source else source
            
            vector_duplicates[original_filename].append(source)
        
        print("   Vector duplicates by original filename:")
        for original_filename, sources in vector_duplicates.items():
            if len(sources) > 1:
                print(f"     {original_filename}: {len(sources)} vector entries")
                for source in sources:
                    print(f"       {source}")
    
    except Exception as e:
        print(f"   ❌ Error analyzing vector database: {e}")
        vector_duplicates = {}
    
    return db_duplicates, fs_duplicates, vector_duplicates

def cleanup_duplicates(db_duplicates, fs_duplicates, vector_duplicates, dry_run=True):
    """Clean up duplicates across all three systems"""
    action_type = "DRY RUN" if dry_run else "CLEANUP"
    print(f"\n🧹 COMPREHENSIVE DUPLICATE CLEANUP - {action_type}")
    print("=" * 50)
    
    cleanup_summary = {
        'db_records_removed': 0,
        'fs_files_removed': 0,
        'vector_entries_removed': 0,
        'space_freed_mb': 0.0,
        'errors': []
    }
    
    # Find all unique original filenames that have duplicates
    all_duplicated_files = set()
    for original_filename, records in db_duplicates.items():
        if len(records) > 1:
            all_duplicated_files.add(original_filename)
    
    for original_filename, paths in fs_duplicates.items():
        if len(paths) > 1:
            all_duplicated_files.add(original_filename)
    
    for original_filename, sources in vector_duplicates.items():
        if len(sources) > 1:
            all_duplicated_files.add(original_filename)
    
    print(f"Found {len(all_duplicated_files)} files with duplicates across systems")
    
    for original_filename in all_duplicated_files:
        print(f"\nProcessing duplicates for: {original_filename}")
        
        # 1. Database cleanup - keep most recent record
        if original_filename in db_duplicates and len(db_duplicates[original_filename]) > 1:
            records = db_duplicates[original_filename]
            # Sort by created_at DESC (most recent first)
            sorted_records = sorted(records, key=lambda x: x[4], reverse=True)
            keep_record = sorted_records[0]
            remove_records = sorted_records[1:]
            
            print(f"  Database: Keep ID {keep_record[0]} ({keep_record[4]}), remove {len(remove_records)} records")
            
            if not dry_run:
                try:
                    conn = sqlite3.connect('erdb_main.db')
                    cursor = conn.cursor()
                    for record in remove_records:
                        cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (record[0],))
                        cleanup_summary['db_records_removed'] += 1
                        print(f"    Removed database record ID: {record[0]}")
                    conn.commit()
                    conn.close()
                except Exception as e:
                    error_msg = f"Error removing database records for {original_filename}: {e}"
                    cleanup_summary['errors'].append(error_msg)
                    print(f"    ❌ {error_msg}")
            else:
                cleanup_summary['db_records_removed'] += len(remove_records)
        
        # 2. Filesystem cleanup - keep most recent file
        if original_filename in fs_duplicates and len(fs_duplicates[original_filename]) > 1:
            paths = fs_duplicates[original_filename]
            # Sort by modification time (most recent first)
            sorted_paths = sorted(paths, key=lambda x: os.path.getmtime(x), reverse=True)
            keep_path = sorted_paths[0]
            remove_paths = sorted_paths[1:]
            
            print(f"  Filesystem: Keep {keep_path}, remove {len(remove_paths)} files")
            
            if not dry_run:
                for path in remove_paths:
                    try:
                        if os.path.exists(path):
                            file_size = os.path.getsize(path)
                            os.remove(path)
                            cleanup_summary['fs_files_removed'] += 1
                            cleanup_summary['space_freed_mb'] += file_size / (1024 * 1024)
                            print(f"    Removed filesystem file: {path}")
                            
                            # Remove empty parent directory
                            parent_dir = os.path.dirname(path)
                            if os.path.exists(parent_dir) and not os.listdir(parent_dir):
                                os.rmdir(parent_dir)
                                print(f"    Removed empty directory: {parent_dir}")
                    except Exception as e:
                        error_msg = f"Error removing file {path}: {e}"
                        cleanup_summary['errors'].append(error_msg)
                        print(f"    ❌ {error_msg}")
            else:
                for path in remove_paths:
                    if os.path.exists(path):
                        file_size = os.path.getsize(path)
                        cleanup_summary['fs_files_removed'] += 1
                        cleanup_summary['space_freed_mb'] += file_size / (1024 * 1024)
        
        # 3. Vector database cleanup - keep most recent entry
        if original_filename in vector_duplicates and len(vector_duplicates[original_filename]) > 1:
            sources = vector_duplicates[original_filename]
            # Sort by timestamp in filename (most recent first)
            sorted_sources = sorted(sources, reverse=True)
            keep_source = sorted_sources[0]
            remove_sources = sorted_sources[1:]
            
            print(f"  Vector DB: Keep {keep_source}, remove {len(remove_sources)} entries")
            
            if not dry_run:
                try:
                    from app.utils.helpers import get_vector_db
                    db = get_vector_db('CANOPY')
                    
                    for source in remove_sources:
                        # Delete vector entries for this source
                        db.delete(filter={"source": source})
                        cleanup_summary['vector_entries_removed'] += 1
                        print(f"    Removed vector entries for: {source}")
                except Exception as e:
                    error_msg = f"Error removing vector entries for {original_filename}: {e}"
                    cleanup_summary['errors'].append(error_msg)
                    print(f"    ❌ {error_msg}")
            else:
                cleanup_summary['vector_entries_removed'] += len(remove_sources)
    
    # Print summary
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"Database records removed: {cleanup_summary['db_records_removed']}")
    print(f"Filesystem files removed: {cleanup_summary['fs_files_removed']}")
    print(f"Vector entries removed: {cleanup_summary['vector_entries_removed']}")
    print(f"Space freed: {cleanup_summary['space_freed_mb']:.2f} MB")
    print(f"Errors: {len(cleanup_summary['errors'])}")
    
    if cleanup_summary['errors']:
        print("\nErrors encountered:")
        for error in cleanup_summary['errors']:
            print(f"  - {error}")
    
    if dry_run:
        print("\n⚠️  DRY RUN - No changes made. Run with dry_run=False to execute cleanup.")
    else:
        print("\n✅ CLEANUP COMPLETED!")
    
    return cleanup_summary

def main():
    """Main restoration function"""
    print("🚨 CRITICAL SYSTEM RESTORATION")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    
    # Step 1: Analyze current state
    db_duplicates, fs_duplicates, vector_duplicates = analyze_current_state()
    
    # Step 2: Show what will be cleaned up
    cleanup_summary = cleanup_duplicates(db_duplicates, fs_duplicates, vector_duplicates, dry_run=True)
    
    # Step 3: Ask for confirmation
    if (cleanup_summary['db_records_removed'] > 0 or 
        cleanup_summary['fs_files_removed'] > 0 or 
        cleanup_summary['vector_entries_removed'] > 0):
        
        print("\n" + "=" * 60)
        response = input("Execute cleanup to restore system? (y/N): ").strip().lower()
        
        if response == 'y':
            cleanup_duplicates(db_duplicates, fs_duplicates, vector_duplicates, dry_run=False)
            print("\n🎉 SYSTEM RESTORATION COMPLETED!")
            print("The ERDB system should now be working correctly:")
            print("  ✅ Vector data will be displayed in Manage Files interface")
            print("  ✅ No duplicate files in any system")
            print("  ✅ All three systems synchronized")
        else:
            print("Cleanup cancelled. System remains in broken state.")
    else:
        print("\n✅ No duplicates found - system appears to be clean!")

if __name__ == '__main__':
    main()
