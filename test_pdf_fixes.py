#!/usr/bin/env python3
"""
Test script for PDF duplicate detection and OCR conversion fixes.
"""

import os
import sys
import tempfile
import shutil
import logging
from io import BytesIO

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_pdf(content="Test PDF Content"):
    """Create a simple test PDF file."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        buffer = BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        p.drawString(100, 750, content)
        p.showPage()
        p.save()
        
        buffer.seek(0)
        return buffer
    except ImportError:
        logger.error("reportlab not available, creating dummy PDF")
        # Create a minimal PDF structure
        pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(""" + content.encode() + b""") Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
295
%%EOF"""
        return BytesIO(pdf_content)

class MockFile:
    """Mock file object for testing."""
    def __init__(self, content, filename):
        self.content = content
        self.filename = filename
        self.position = 0
    
    def read(self, size=-1):
        if size == -1:
            data = self.content[self.position:]
            self.position = len(self.content)
        else:
            data = self.content[self.position:self.position + size]
            self.position += len(data)
        return data
    
    def seek(self, position):
        self.position = position
    
    def tell(self):
        return self.position
    
    def save(self, path):
        with open(path, 'wb') as f:
            f.write(self.content)

def test_duplicate_detection():
    """Test the duplicate detection functionality."""
    logger.info("Testing duplicate detection...")
    
    try:
        from app.utils.helpers import check_duplicate_pdf, calculate_file_hash
        
        # Create test PDF content
        pdf_buffer = create_test_pdf("Test content for duplicate detection")
        pdf_content = pdf_buffer.getvalue()
        
        # Create mock file objects
        file1 = MockFile(pdf_content, "test_document.pdf")
        file2 = MockFile(pdf_content, "test_document.pdf")  # Same name
        file3 = MockFile(pdf_content, "different_name.pdf")  # Same content, different name
        
        # Test 1: Check hash calculation
        hash1 = calculate_file_hash(file1)
        hash2 = calculate_file_hash(file2)
        hash3 = calculate_file_hash(file3)
        
        assert hash1 == hash2 == hash3, "Hashes should be identical for same content"
        logger.info("✓ Hash calculation working correctly")
        
        # Test 2: Check duplicate detection (this will fail if no database exists)
        try:
            is_duplicate, info = check_duplicate_pdf(file1, "TEST")
            logger.info(f"✓ Duplicate detection function executed: duplicate={is_duplicate}")
        except Exception as e:
            logger.warning(f"Duplicate detection test skipped (database not available): {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Duplicate detection test failed: {e}")
        return False

def test_ocr_conversion_service():
    """Test the OCR conversion service."""
    logger.info("Testing OCR conversion service...")
    
    try:
        from app.services.ocr_conversion_service import get_ocr_conversion_service
        
        # Get the service
        ocr_service = get_ocr_conversion_service()
        logger.info("✓ OCR conversion service instantiated")
        
        # Test service methods exist
        assert hasattr(ocr_service, 'convert_pdf_ocr_to_non_ocr'), "convert_pdf_ocr_to_non_ocr method missing"
        assert hasattr(ocr_service, 'convert_existing_pdf'), "convert_existing_pdf method missing"
        assert hasattr(ocr_service, 'update_database_after_conversion'), "update_database_after_conversion method missing"
        assert hasattr(ocr_service, 'update_vector_database_after_conversion'), "update_vector_database_after_conversion method missing"
        
        logger.info("✓ OCR conversion service has all required methods")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ OCR conversion service test failed: {e}")
        return False

def test_api_endpoints():
    """Test that the API endpoints are properly defined."""
    logger.info("Testing API endpoints...")
    
    try:
        # This is a basic test to ensure the routes are importable
        from app.routes.api import api_bp
        
        # Check if our new routes are registered
        route_names = [rule.rule for rule in api_bp.url_map.iter_rules()]
        
        expected_routes = ['/convert_ocr_pdf', '/detect_ocr_pdf']
        
        for route in expected_routes:
            if any(route in rule for rule in route_names):
                logger.info(f"✓ Route {route} found")
            else:
                logger.warning(f"Route {route} not found in registered routes")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ API endpoints test failed: {e}")
        return False

def test_chroma_manager_integration():
    """Test that the ChromaDB manager integration works."""
    logger.info("Testing ChromaDB manager integration...")
    
    try:
        from app.services.chroma_manager import get_chroma_manager, get_unified_chroma_db
        
        # Test manager instantiation
        manager = get_chroma_manager()
        logger.info("✓ ChromaDB manager instantiated")
        
        # Test unified DB access
        try:
            db = get_unified_chroma_db()
            logger.info("✓ Unified ChromaDB instance accessible")
        except Exception as e:
            logger.warning(f"ChromaDB instance test skipped (dependencies not available): {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ ChromaDB manager integration test failed: {e}")
        return False

def test_database_migration():
    """Test that the database migration script is available."""
    logger.info("Testing database migration availability...")
    
    try:
        migration_path = "scripts/migrations/add_file_hash_column.py"
        
        if os.path.exists(migration_path):
            logger.info("✓ File hash migration script exists")
            
            # Try to import the migration function
            sys.path.insert(0, os.path.dirname(migration_path))
            import add_file_hash_column
            
            assert hasattr(add_file_hash_column, 'add_file_hash_column'), "Migration function missing"
            logger.info("✓ Migration function is importable")
            
        else:
            logger.error(f"✗ Migration script not found: {migration_path}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Database migration test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting PDF fixes test suite...")
    
    tests = [
        ("Duplicate Detection", test_duplicate_detection),
        ("OCR Conversion Service", test_ocr_conversion_service),
        ("API Endpoints", test_api_endpoints),
        ("ChromaDB Manager Integration", test_chroma_manager_integration),
        ("Database Migration", test_database_migration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} test PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
    
    logger.info(f"\n--- Test Results ---")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Total: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.error(f"❌ {failed} test(s) failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
