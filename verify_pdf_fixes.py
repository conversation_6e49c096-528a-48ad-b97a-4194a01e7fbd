#!/usr/bin/env python3
"""
Verification script for PDF duplicate detection and OCR conversion fixes.
This script verifies the code structure without requiring the full Flask environment.
"""

import os
import ast
import sys

def check_file_exists(filepath):
    """Check if a file exists and return its status."""
    if os.path.exists(filepath):
        print(f"✓ {filepath} exists")
        return True
    else:
        print(f"✗ {filepath} missing")
        return False

def check_function_exists(filepath, function_name):
    """Check if a function exists in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == function_name:
                print(f"  ✓ Function {function_name} found")
                return True
        
        print(f"  ✗ Function {function_name} not found")
        return False
        
    except Exception as e:
        print(f"  ✗ Error checking function in {filepath}: {e}")
        return False

def check_class_exists(filepath, class_name):
    """Check if a class exists in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                print(f"  ✓ Class {class_name} found")
                return True
        
        print(f"  ✗ Class {class_name} not found")
        return False
        
    except Exception as e:
        print(f"  ✗ Error checking class in {filepath}: {e}")
        return False

def check_string_in_file(filepath, search_string):
    """Check if a string exists in a file."""
    if not os.path.exists(filepath):
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if search_string in content:
            print(f"  ✓ String '{search_string}' found")
            return True
        else:
            print(f"  ✗ String '{search_string}' not found")
            return False
        
    except Exception as e:
        print(f"  ✗ Error checking string in {filepath}: {e}")
        return False

def main():
    """Run verification checks."""
    print("PDF Fixes Verification")
    print("=" * 50)
    
    all_checks_passed = True
    
    # Check 1: Enhanced duplicate detection
    print("\n1. Checking Enhanced Duplicate Detection:")
    helpers_path = "app/utils/helpers.py"
    if check_file_exists(helpers_path):
        if not check_function_exists(helpers_path, "check_duplicate_pdf"):
            all_checks_passed = False
        if not check_string_in_file(helpers_path, "get_pdf_by_hash"):
            all_checks_passed = False
        if not check_string_in_file(helpers_path, "content_match"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 2: Database functions for hash-based duplicate detection
    print("\n2. Checking Database Hash Functions:")
    content_db_path = "app/utils/content_db.py"
    if check_file_exists(content_db_path):
        if not check_function_exists(content_db_path, "get_pdf_by_hash"):
            all_checks_passed = False
        if not check_string_in_file(content_db_path, "file_hash"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 3: Updated embed_file_db_first with duplicate detection
    print("\n3. Checking Updated embed_file_db_first:")
    embedding_db_path = "app/utils/embedding_db.py"
    if check_file_exists(embedding_db_path):
        if not check_string_in_file(embedding_db_path, "skip_duplicate_check"):
            all_checks_passed = False
        if not check_string_in_file(embedding_db_path, "check_duplicate_pdf"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 4: OCR conversion service
    print("\n4. Checking OCR Conversion Service:")
    ocr_service_path = "app/services/ocr_conversion_service.py"
    if check_file_exists(ocr_service_path):
        if not check_class_exists(ocr_service_path, "OCRConversionService"):
            all_checks_passed = False
        if not check_function_exists(ocr_service_path, "get_ocr_conversion_service"):
            all_checks_passed = False
        if not check_string_in_file(ocr_service_path, "convert_pdf_ocr_to_non_ocr"):
            all_checks_passed = False
        if not check_string_in_file(ocr_service_path, "update_vector_database_after_conversion"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 5: API endpoints for OCR conversion
    print("\n5. Checking OCR API Endpoints:")
    api_routes_path = "app/routes/api.py"
    if check_file_exists(api_routes_path):
        if not check_string_in_file(api_routes_path, "/convert_ocr_pdf"):
            all_checks_passed = False
        if not check_string_in_file(api_routes_path, "/detect_ocr_pdf"):
            all_checks_passed = False
        if not check_string_in_file(api_routes_path, "get_ocr_conversion_service"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 6: Updated API upload route with duplicate handling
    print("\n6. Checking Updated Upload Route:")
    if check_file_exists(api_routes_path):
        if not check_string_in_file(api_routes_path, "duplicate_action"):
            all_checks_passed = False
        if not check_string_in_file(api_routes_path, "is_duplicate"):
            all_checks_passed = False
        if not check_string_in_file(api_routes_path, "convert_to_non_ocr"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 7: Database migration script
    print("\n7. Checking Database Migration:")
    migration_path = "scripts/migrations/add_file_hash_column.py"
    if check_file_exists(migration_path):
        if not check_function_exists(migration_path, "add_file_hash_column"):
            all_checks_passed = False
        if not check_string_in_file(migration_path, "ADD COLUMN file_hash"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Check 8: ChromaDB manager integration
    print("\n8. Checking ChromaDB Manager Integration:")
    chroma_manager_path = "app/services/chroma_manager.py"
    if check_file_exists(chroma_manager_path):
        if not check_string_in_file(ocr_service_path, "get_unified_chroma_db"):
            all_checks_passed = False
    else:
        all_checks_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 All verification checks passed!")
        print("\nNext steps:")
        print("1. Run the database migration: python scripts/migrations/add_file_hash_column.py")
        print("2. Test duplicate detection with actual PDF uploads")
        print("3. Test OCR conversion functionality")
        print("4. Monitor logs for any issues")
        print("\nAPI Endpoints added:")
        print("- POST /api/convert_ocr_pdf - Convert existing PDF from OCR to non-OCR")
        print("- POST /api/detect_ocr_pdf - Detect if PDF contains OCR text layers")
        print("\nUpload endpoint enhanced:")
        print("- POST /api/upload - Now supports duplicate detection and OCR conversion")
    else:
        print("❌ Some verification checks failed!")
        print("Please review the issues above and fix them before proceeding.")
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
