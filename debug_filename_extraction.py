#!/usr/bin/env python3
"""
Debug script to understand filename extraction logic
"""

import sqlite3

def debug_filename_extraction():
    print("🔍 FILENAME EXTRACTION DEBUG")
    print("=" * 50)
    
    # Get database records
    conn = sqlite3.connect('erdb_main.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, filename, original_filename, category FROM pdf_documents WHERE category = "CANOPY" ORDER BY created_at DESC')
    db_records = cursor.fetchall()
    conn.close()
    
    print("📊 Database records:")
    for record in db_records:
        print(f"  ID={record[0]}")
        print(f"    filename: '{record[1]}'")
        print(f"    original_filename: '{record[2]}'")
        print(f"    category: '{record[3]}'")
        print()
    
    # Test filesystem filename extraction
    filesystem_files = [
        'non_ocr_20250717082043_CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf',
        'non_ocr_20250717082043_CANOPY_INTERNATIONAL_VOL_1_NO_2.pdf',
        'non_ocr_20250717082622_CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf',
        'non_ocr_20250717082622_CANOPY_INTERNATIONAL_VOL_1_NO_2.pdf',
        'non_ocr_20250717083324_CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf',
        'non_ocr_20250717083324_CANOPY_INTERNATIONAL_VOL_1_NO_2.pdf'
    ]
    
    print("📁 Filesystem filename extraction:")
    for filename in filesystem_files:
        print(f"  Input: '{filename}'")
        
        # Apply the same logic as in the cleanup script
        if filename.startswith('non_ocr_'):
            original = filename[8:]  # Remove 'non_ocr_' prefix
        elif filename.startswith('ocr_'):
            original = filename[4:]  # Remove 'ocr_' prefix
        else:
            original = filename.split('_', 1)[1] if '_' in filename else filename
        
        print(f"    Extracted: '{original}'")
        
        # Check for matches with database
        matches = []
        for record in db_records:
            db_original = record[2]
            if db_original == original:
                matches.append(f"EXACT: {record[1]}")
            elif db_original.replace(' ', '_') == original.replace(' ', '_'):
                matches.append(f"SPACE/UNDERSCORE: {record[1]}")
            elif db_original.lower() == original.lower():
                matches.append(f"CASE: {record[1]}")
        
        if matches:
            print(f"    Matches: {matches}")
        else:
            print(f"    Matches: NONE")
        print()
    
    print("🔧 Testing different extraction methods:")
    test_filename = 'non_ocr_20250717083324_CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf'
    db_original = 'CANOPY INTERNATIONAL VOL 1 NO 1.pdf'
    
    print(f"Test filename: '{test_filename}'")
    print(f"Database original: '{db_original}'")
    
    # Method 1: Current logic
    extracted1 = test_filename[8:]  # Remove 'non_ocr_'
    print(f"Method 1 (remove non_ocr_): '{extracted1}'")
    print(f"  Match with DB: {extracted1 == db_original}")
    print(f"  Match with spaces->underscores: {extracted1.replace('_', ' ') == db_original}")
    
    # Method 2: Remove timestamp and non_ocr_
    parts = test_filename.split('_')
    if len(parts) >= 3 and parts[0] == 'non' and parts[1] == 'ocr':
        # Remove 'non_ocr_' and timestamp
        extracted2 = '_'.join(parts[3:])  # Skip 'non', 'ocr', timestamp
        print(f"Method 2 (remove non_ocr_ + timestamp): '{extracted2}'")
        print(f"  Match with spaces: {extracted2.replace('_', ' ') == db_original}")

if __name__ == "__main__":
    debug_filename_extraction()
