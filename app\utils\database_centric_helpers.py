#!/usr/bin/env python3
"""
Database-Centric Helper Functions for ERDB System

This module provides helper functions for the new database-centric architecture
where all file storage and operations are handled through the database only.

Key Features:
- Hash-based duplicate detection
- BLOB storage for PDF files and cover images
- Database-only file operations
- Simplified single-source architecture
"""

import hashlib
import sqlite3
import io
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def calculate_file_hash(file_content: bytes) -> str:
    """
    Calculate SHA-256 hash of file content
    
    Args:
        file_content: Binary content of the file
        
    Returns:
        SHA-256 hash as hexadecimal string
    """
    return hashlib.sha256(file_content).hexdigest()

def check_duplicate_by_hash(file_hash: str, category: str = None) -> Dict[str, Any]:
    """
    Check for duplicate files using hash-based detection (database-centric)
    
    Args:
        file_hash: SHA-256 hash of the file
        category: Optional category to limit search
        
    Returns:
        Dictionary with duplicate detection results
    """
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Build query based on whether category is specified
        if category:
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at, file_size
                FROM pdf_documents 
                WHERE file_hash = ? AND category = ?
                ORDER BY created_at DESC
            ''', (file_hash, category))
        else:
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at, file_size
                FROM pdf_documents 
                WHERE file_hash = ?
                ORDER BY created_at DESC
            ''', (file_hash,))
        
        duplicates = cursor.fetchall()
        conn.close()
        
        if duplicates:
            return {
                'is_duplicate': True,
                'duplicate_count': len(duplicates),
                'duplicates': [
                    {
                        'id': dup[0],
                        'filename': dup[1],
                        'original_filename': dup[2],
                        'category': dup[3],
                        'created_at': dup[4],
                        'file_size': dup[5]
                    } for dup in duplicates
                ],
                'message': f'Found {len(duplicates)} duplicate file(s) with identical content'
            }
        else:
            return {
                'is_duplicate': False,
                'duplicate_count': 0,
                'duplicates': [],
                'message': 'No duplicates found'
            }
            
    except Exception as e:
        logger.error(f"Error checking duplicates by hash: {e}")
        return {
            'is_duplicate': False,
            'duplicate_count': 0,
            'duplicates': [],
            'message': f'Error checking duplicates: {str(e)}',
            'error': str(e)
        }

def check_duplicate_by_filename(original_filename: str, category: str) -> Dict[str, Any]:
    """
    Check for duplicate files by original filename (database-centric)
    
    Args:
        original_filename: Original name of the file
        category: Category to search in
        
    Returns:
        Dictionary with duplicate detection results
    """
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, file_hash
            FROM pdf_documents 
            WHERE original_filename = ? AND category = ?
            ORDER BY created_at DESC
        ''', (original_filename, category))
        
        duplicates = cursor.fetchall()
        conn.close()
        
        if duplicates:
            return {
                'is_duplicate': True,
                'duplicate_count': len(duplicates),
                'duplicates': [
                    {
                        'id': dup[0],
                        'filename': dup[1],
                        'original_filename': dup[2],
                        'category': dup[3],
                        'created_at': dup[4],
                        'file_size': dup[5],
                        'file_hash': dup[6]
                    } for dup in duplicates
                ],
                'message': f'Found {len(duplicates)} file(s) with the same filename'
            }
        else:
            return {
                'is_duplicate': False,
                'duplicate_count': 0,
                'duplicates': [],
                'message': 'No filename duplicates found'
            }
            
    except Exception as e:
        logger.error(f"Error checking duplicates by filename: {e}")
        return {
            'is_duplicate': False,
            'duplicate_count': 0,
            'duplicates': [],
            'message': f'Error checking duplicates: {str(e)}',
            'error': str(e)
        }

def extract_cover_image(pdf_content: bytes) -> Tuple[Optional[bytes], str]:
    """
    Extract cover image (first page) from PDF content
    
    Args:
        pdf_content: Binary PDF content
        
    Returns:
        Tuple of (cover_image_bytes, format) or (None, "")
    """
    try:
        import fitz  # PyMuPDF
        
        # Open PDF from memory
        doc = fitz.open(stream=pdf_content, filetype="pdf")
        
        if len(doc) > 0:
            # Get first page
            page = doc[0]
            
            # Render page as image
            pix = page.get_pixmap()
            
            # Convert to PNG bytes
            cover_image_bytes = pix.tobytes("png")
            
            doc.close()
            return cover_image_bytes, "PNG"
        else:
            doc.close()
            return None, ""
            
    except Exception as e:
        logger.error(f"Error extracting cover image: {e}")
        return None, ""

def store_pdf_in_database(
    pdf_content: bytes,
    original_filename: str,
    category: str,
    file_size: int,
    page_count: int = None,
    form_id: int = None
) -> Dict[str, Any]:
    """
    Store PDF file and metadata in database (database-centric)
    
    Args:
        pdf_content: Binary PDF content
        original_filename: Original name of the file
        category: Category for the file
        file_size: Size of the file in bytes
        page_count: Number of pages in PDF
        form_id: Optional form ID for gated downloads
        
    Returns:
        Dictionary with storage results
    """
    try:
        # Calculate file hash
        file_hash = calculate_file_hash(pdf_content)
        
        # Extract cover image
        cover_image_blob, cover_image_format = extract_cover_image(pdf_content)
        
        # Generate unique filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        filename = f"{timestamp}_{original_filename}"
        
        # Store in database
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO pdf_documents (
                filename, original_filename, category, file_size, page_count,
                pdf_content_blob, cover_image_blob, cover_image_format,
                file_hash, form_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            filename, original_filename, category, file_size, page_count,
            pdf_content, cover_image_blob, cover_image_format,
            file_hash, form_id, datetime.now()
        ))
        
        pdf_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'pdf_id': pdf_id,
            'filename': filename,
            'file_hash': file_hash,
            'cover_image_extracted': cover_image_blob is not None,
            'message': f'PDF stored successfully in database (ID: {pdf_id})'
        }
        
    except Exception as e:
        logger.error(f"Error storing PDF in database: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': f'Failed to store PDF in database: {str(e)}'
        }

def get_pdf_from_database(pdf_id: int) -> Dict[str, Any]:
    """
    Retrieve PDF file from database
    
    Args:
        pdf_id: Database ID of the PDF
        
    Returns:
        Dictionary with PDF data or error
    """
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT pdf_content_blob, original_filename, category, file_size, 
                   cover_image_blob, cover_image_format, created_at
            FROM pdf_documents 
            WHERE id = ?
        ''', (pdf_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'success': True,
                'pdf_content': result[0],
                'original_filename': result[1],
                'category': result[2],
                'file_size': result[3],
                'cover_image': result[4],
                'cover_image_format': result[5],
                'created_at': result[6]
            }
        else:
            return {
                'success': False,
                'error': 'PDF not found',
                'message': f'No PDF found with ID: {pdf_id}'
            }
            
    except Exception as e:
        logger.error(f"Error retrieving PDF from database: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': f'Failed to retrieve PDF from database: {str(e)}'
        }

def list_database_files(category: str = None) -> List[Dict[str, Any]]:
    """
    List all files stored in database (database-centric)
    
    Args:
        category: Optional category filter
        
    Returns:
        List of file dictionaries
    """
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        if category:
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at,
                       file_size, page_count, file_hash, form_id,
                       CASE WHEN cover_image_blob IS NOT NULL THEN 1 ELSE 0 END as has_cover
                FROM pdf_documents 
                WHERE category = ?
                ORDER BY created_at DESC
            ''', (category,))
        else:
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at,
                       file_size, page_count, file_hash, form_id,
                       CASE WHEN cover_image_blob IS NOT NULL THEN 1 ELSE 0 END as has_cover
                FROM pdf_documents 
                ORDER BY category, created_at DESC
            ''')
        
        records = cursor.fetchall()
        conn.close()
        
        files = []
        for record in records:
            files.append({
                'id': record[0],
                'filename': record[1],
                'original_filename': record[2],
                'category': record[3],
                'created_at': record[4],
                'file_size': record[5],
                'page_count': record[6],
                'file_hash': record[7],
                'form_id': record[8],
                'has_cover_image': bool(record[9]),
                'type': 'pdf'
            })
        
        return files
        
    except Exception as e:
        logger.error(f"Error listing database files: {e}")
        return []

def delete_pdf_from_database(pdf_id: int) -> Dict[str, Any]:
    """
    Delete PDF file from database (database-centric)
    
    Args:
        pdf_id: Database ID of the PDF to delete
        
    Returns:
        Dictionary with deletion results
    """
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Get file info before deletion
        cursor.execute('''
            SELECT original_filename, category, file_size
            FROM pdf_documents 
            WHERE id = ?
        ''', (pdf_id,))
        
        file_info = cursor.fetchone()
        
        if not file_info:
            conn.close()
            return {
                'success': False,
                'error': 'File not found',
                'message': f'No file found with ID: {pdf_id}'
            }
        
        # Delete the record
        cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (pdf_id,))
        
        # Also delete any related form submissions
        cursor.execute('DELETE FROM form_submissions WHERE pdf_document_id = ?', (pdf_id,))
        
        conn.commit()
        conn.close()
        
        return {
            'success': True,
            'filename': file_info[0],
            'category': file_info[1],
            'file_size': file_info[2],
            'message': f'PDF deleted successfully from database (ID: {pdf_id})'
        }
        
    except Exception as e:
        logger.error(f"Error deleting PDF from database: {e}")
        return {
            'success': False,
            'error': str(e),
            'message': f'Failed to delete PDF from database: {str(e)}'
        }
