#!/usr/bin/env python3
"""
Simple test to verify duplicate detection is working.
This test simulates the duplicate detection process without requiring Flask dependencies.
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_duplicate_detection_query():
    """Test the duplicate detection database query directly"""
    print("🧪 Testing Duplicate Detection Database Query")
    print("=" * 50)
    
    try:
        # Connect to database
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Get an existing file to test with
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        
        existing_record = cursor.fetchone()
        if not existing_record:
            print("❌ No existing records found in database")
            return False
        
        record_id, filename, original_filename, category = existing_record
        print(f"📄 Testing with existing record:")
        print(f"   ID: {record_id}")
        print(f"   Filename: {filename}")
        print(f"   Original filename: {original_filename}")
        print(f"   Category: {category}")
        print()
        
        # Test 1: Query by original_filename (this is what duplicate detection uses)
        print("🔍 Test 1: Query by original_filename")
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            WHERE original_filename = ? AND category = ?
            ORDER BY created_at DESC
            LIMIT 1
        ''', (original_filename, category))
        
        result = cursor.fetchone()
        if result:
            print("   ✅ SUCCESS: Found matching record")
            print(f"   Result: ID={result[0]}, filename={result[1]}, original_filename={result[2]}, category={result[3]}")
        else:
            print("   ❌ FAILED: No matching record found")
            return False
        print()
        
        # Test 2: Test with a non-existent file
        print("🔍 Test 2: Query for non-existent file")
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            WHERE original_filename = ? AND category = ?
            ORDER BY created_at DESC
            LIMIT 1
        ''', ("non_existent_file.pdf", category))
        
        result = cursor.fetchone()
        if result:
            print("   ❌ FAILED: Found record for non-existent file (unexpected)")
            return False
        else:
            print("   ✅ SUCCESS: No record found for non-existent file (expected)")
        print()
        
        # Test 3: Check file_hash column exists and works
        print("🔍 Test 3: Check file_hash column")
        cursor.execute('''
            SELECT id, filename, original_filename, category, file_hash
            FROM pdf_documents
            WHERE id = ?
        ''', (record_id,))
        
        result = cursor.fetchone()
        if result:
            file_hash = result[4]
            print("   ✅ SUCCESS: file_hash column accessible")
            print(f"   File hash: {file_hash if file_hash else 'NULL'}")
        else:
            print("   ❌ FAILED: Could not access file_hash column")
            return False
        print()
        
        print("✅ All database queries working correctly!")
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

def simulate_duplicate_check():
    """Simulate the duplicate check process"""
    print("\n🎯 Simulating Duplicate Check Process")
    print("=" * 50)
    
    try:
        # Get an existing file to simulate uploading
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT original_filename, category
            FROM pdf_documents
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        
        record = cursor.fetchone()
        if not record:
            print("❌ No existing records to test with")
            return False
        
        original_filename, category = record
        print(f"📤 Simulating upload of: {original_filename}")
        print(f"📁 Category: {category}")
        print()
        
        # Step 1: Check for filename-based duplicate (this is the primary check)
        print("🔍 Step 1: Checking for filename-based duplicate...")
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            WHERE original_filename = ? AND category = ?
            ORDER BY created_at DESC
            LIMIT 1
        ''', (original_filename, category))
        
        existing_pdf = cursor.fetchone()
        if existing_pdf:
            print("   ✅ DUPLICATE DETECTED by filename!")
            print(f"   Existing record: ID={existing_pdf[0]}, filename={existing_pdf[1]}")
            print(f"   This would trigger: 'Duplicate PDF detected: A file with the name '{original_filename}' already exists in category '{category}' (stored as '{existing_pdf[1]}')'")
            return True
        else:
            print("   ❌ No duplicate detected (this would be a bug!)")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 Duplicate Detection Test Suite")
    print("=" * 60)
    
    # Test 1: Database queries
    db_test_passed = test_duplicate_detection_query()
    
    # Test 2: Simulate duplicate check
    if db_test_passed:
        duplicate_test_passed = simulate_duplicate_check()
    else:
        duplicate_test_passed = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)
    print(f"Database queries: {'✅ PASSED' if db_test_passed else '❌ FAILED'}")
    print(f"Duplicate detection: {'✅ PASSED' if duplicate_test_passed else '❌ FAILED'}")
    
    if db_test_passed and duplicate_test_passed:
        print("\n🎉 All tests passed! Duplicate detection should be working.")
        print("\nTo test with actual uploads:")
        print("1. Upload any PDF file to the system")
        print("2. Try uploading the same file again")
        print("3. The system should detect it as a duplicate")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
