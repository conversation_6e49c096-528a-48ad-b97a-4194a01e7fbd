import os
import logging
import json
import requests
import shutil
import re

from dotenv import load_dotenv

from flask import Flask, request, jsonify, render_template, flash, redirect, url_for, send_file, session
from werkzeug.utils import secure_filename
from app.services.query_service import query_category
from app.services.embedding_service import embed_file, scrape_url
from app.utils import helpers as utils
from app.utils import database as db_utils
from app.services import geo_service as geo_utils
from app.services import geo_analytics as geoip_analytics
from app.services.optimized_vector_db import get_vector_db, OLLAMA_BASE_URL
from app.utils.helpers import list_categories, delete_file, check_duplicate_pdf
from app.utils.validate_and_italicize_scientific_names import validate_and_italicize_scientific_names
import ollama
from scripts.setup.create_temp_dirs import create_temp_directories
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.embedding_db import embed_file_db_first, scrape_url_db_first
from app.services import user_service as um
from app.routes.user import user_bp
from app.services.greeting_service import GreetingManager
greeting_manager = GreetingManager()
from app.services.html_generator import html_generator
from app.utils.error_handlers import register_error_handlers
from app.routes.main import main_bp
#Looking at the lint errors and the file context, I can see the issue is with the import formatting. The long import line from `app.utils.content_db` needs to be properly formatted with parentheses for better readability and to avoid linting issues.
from app.utils.helpers import TEMP_FOLDER
import fitz # PyMuPDF
from datetime import datetime
from app.routes.public_api import public_api_bp
from app.utils.security import csrf

from app.routes.admin import admin_bp
from app.utils.config import Config
from app.utils import forms_db
#from app.utils.helpers import extract_cover_image_from_pdf
from app.utils.content_db import create_gated_pdf_record, get_pdf_by_filename_only, get_pdf_by_id, get_source_url_by_id
from app.utils.forms_db import get_form_submission
from app.utils.config import get_models_data, get_query_config_data, get_embedding_config_data
from flask_login import current_user
from app.utils.permissions import admin_required, function_permission_required
from app.services.pdf_processor import extract_cover_image_from_pdf

# Get the directory where this script is located
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# Go up one level to the project root
PROJECT_ROOT = os.path.dirname(BASE_DIR)

DEFAULT_MODELS_FILE = os.path.join(PROJECT_ROOT, 'config', 'default_models.json')

# Define paths
UPLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'data', 'temp')
CHROMA_PATH = os.getenv("CHROMA_PATH", "./data/chroma/chroma")

# Environment variables
TEMP_FOLDER = os.getenv("TEMP_FOLDER", os.path.join(PROJECT_ROOT, "data", "temp"))

# Load environment variables from .env file first
load_dotenv()

app = Flask(__name__, static_folder='static', template_folder='templates')
app.config.from_object(Config)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Ensure logs directory exists
os.makedirs(os.path.join(os.path.dirname(__file__), '..', 'logs'), exist_ok=True)

# Set up logging to both file and console
log_file_path = os.path.join(os.path.dirname(__file__), '..', 'logs', 'app.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler(log_file_path, mode='a', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize user management and extensions
login_manager = um.init_login_manager(app)
csrf.init_app(app)
limiter = um.init_limiter(app)

# Exempt static files from rate limiting
@limiter.request_filter
def exempt_static_files():
    """Exempt static files from rate limiting."""
    return request.path.startswith('/static/')



# Register user blueprint
# Blueprints are now registered in app/__init__.py
# This file is kept for backward compatibility
app.register_blueprint(admin_bp)

# Register main blueprint for backward compatibility (others are registered at end of file)
from app.routes.main import main_bp
app.register_blueprint(main_bp)

# Register user blueprint for backward compatibility
app.register_blueprint(user_bp)

# Register backup blueprint
from app.routes.backup import backup_bp
app.register_blueprint(backup_bp)

# Register API blueprint
from app.routes.api import api_bp
app.register_blueprint(api_bp)

# Register global error handlers
register_error_handlers(app)

# Create temporary directories for PDF images and tables
create_temp_directories()

# Default model and embedding
default_llm = 'llama3.1:8b-instruct-q4_K_M'
default_embedding = "mxbai-embed-large:latest"
default_vision = "llama3.2-vision:11b-instruct-q4_K_M"  # Can also use "gemma3:4b-it-q4_K_M" or "gemma3:12b-it-q4_K_M" for vision capabilities

# TEMPORARY: Disable follow-up questions for this session
os.environ['DISABLE_FOLLOWUP_QUESTIONS'] = 'true'

# Function to load default models from file
def load_default_models():
    global default_llm, default_embedding, default_vision

    try:
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)

                # Update default values if they exist in the file
                if 'llm_model' in defaults:
                    default_llm = defaults['llm_model']
                    logger.info(f"Loaded default LLM model from file: {default_llm}")

                if 'embedding_model' in defaults:
                    default_embedding = defaults['embedding_model']
                    logger.info(f"Loaded default embedding model from file: {default_embedding}")

                if 'vision_model' in defaults:
                    default_vision = defaults['vision_model']
                    logger.info(f"Loaded default vision model from file: {default_vision}")

                # Load vision toggle state if it exists
                if 'use_vision_model' in defaults:
                    use_vision = defaults['use_vision_model']
                    use_vision_str = 'true' if use_vision else 'false'
                    os.environ['USE_VISION_MODEL'] = use_vision_str
                    logger.info(f"Loaded vision model enabled setting: {use_vision}")

                # Load vision during embedding toggle state if it exists
                if 'use_vision_model_during_embedding' in defaults:
                    use_vision_during_embedding = defaults['use_vision_model_during_embedding']
                    use_vision_during_embedding_str = 'true' if use_vision_during_embedding else 'false'
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
                    logger.info(f"Loaded vision model during embedding enabled setting: {use_vision_during_embedding}")

                # Load image filtering settings if they exist
                if 'filter_pdf_images' in defaults:
                    filter_pdf_images = defaults['filter_pdf_images']
                    filter_pdf_images_str = 'true' if filter_pdf_images else 'false'
                    os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
                    logger.info(f"Loaded PDF image filtering setting: {filter_pdf_images}")

                if 'filter_sensitivity' in defaults:
                    filter_sensitivity = defaults['filter_sensitivity']
                    if filter_sensitivity in ['low', 'medium', 'high']:
                        os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                        logger.info(f"Loaded PDF image filter sensitivity: {filter_sensitivity}")

                if 'max_pdf_images' in defaults:
                    max_pdf_images = defaults['max_pdf_images']
                    if isinstance(max_pdf_images, int) and 1 <= max_pdf_images <= 50:
                        os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images)
                        logger.info(f"Loaded maximum PDF images to analyze: {max_pdf_images}")

                if 'show_filtered_images' in defaults:
                    show_filtered_images = defaults['show_filtered_images']
                    show_filtered_images_str = 'true' if show_filtered_images else 'false'
                    os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
                    logger.info(f"Loaded show filtered images setting: {show_filtered_images}")

                # Load model parameters if available
                if 'model_parameters' in defaults:
                    model_params_all = defaults['model_parameters']
                    # For legacy compatibility, set environment variables for 'balanced' mode only
                    model_params = model_params_all.get('balanced', {})
                    # Load temperature
                    if 'temperature' in model_params:
                        temperature = model_params['temperature']
                        if isinstance(temperature, (int, float)) and 0 <= temperature <= 1:
                            os.environ['LLM_TEMPERATURE'] = str(temperature)
                            logger.info(f"Loaded LLM temperature: {temperature}")
                    # Load context window size
                    if 'num_ctx' in model_params:
                        num_ctx = model_params['num_ctx']
                        if isinstance(num_ctx, int) and num_ctx > 0:
                            os.environ['LLM_NUM_CTX'] = str(num_ctx)
                            logger.info(f"Loaded LLM context window size: {num_ctx}")
                    # Load max tokens to predict
                    if 'num_predict' in model_params:
                        num_predict = model_params['num_predict']
                        if isinstance(num_predict, int) and num_predict > 0:
                            os.environ['LLM_NUM_PREDICT'] = str(num_predict)
                            logger.info(f"Loaded LLM max tokens to predict: {num_predict}")
                    # Load top_p
                    if 'top_p' in model_params:
                        top_p = model_params['top_p']
                        if isinstance(top_p, (int, float)) and 0 <= top_p <= 1:
                            os.environ['LLM_TOP_P'] = str(top_p)
                            logger.info(f"Loaded LLM top_p: {top_p}")
                    # Load top_k
                    if 'top_k' in model_params:
                        top_k = model_params['top_k']
                        if isinstance(top_k, int) and top_k > 0:
                            os.environ['LLM_TOP_K'] = str(top_k)
                            logger.info(f"Loaded LLM top_k: {top_k}")
                    # Load repeat_penalty
                    if 'repeat_penalty' in model_params:
                        repeat_penalty = model_params['repeat_penalty']
                        if isinstance(repeat_penalty, (int, float)) and repeat_penalty > 0:
                            os.environ['LLM_REPEAT_PENALTY'] = str(repeat_penalty)
                            logger.info(f"Loaded LLM repeat penalty: {repeat_penalty}")
                    # Load system prompt
                    if 'system_prompt' in model_params:
                        system_prompt = model_params['system_prompt']
                        if isinstance(system_prompt, str):
                            os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
                            logger.info(f"Loaded LLM system prompt: {system_prompt[:50]}...")
                # NOTE: Mode-specific model parameters are now handled in query_service.py

                # Load query parameters if available
                if 'query_parameters' in defaults:
                    query_params = defaults['query_parameters']

                    # Load preamble
                    if 'preamble' in query_params:
                        preamble = query_params['preamble']
                        if isinstance(preamble, str):
                            os.environ['QUERY_PREAMBLE'] = preamble
                            logger.info(f"Loaded query preamble: {preamble[:50]}...")

                    # Load anti-hallucination modes
                    if 'anti_hallucination_modes' in query_params:
                        ah_modes = query_params['anti_hallucination_modes']

                        if 'default_mode' in ah_modes:
                            default_mode = ah_modes['default_mode']
                            if default_mode in ['strict', 'balanced', 'off']:
                                os.environ['ANTI_HALLUCINATION_MODE'] = default_mode
                                logger.info(f"Loaded default anti-hallucination mode: {default_mode}")

                        if 'custom_instructions' in ah_modes:
                            custom_instructions = ah_modes['custom_instructions']
                            if isinstance(custom_instructions, str):
                                os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = custom_instructions
                                logger.info(f"Loaded anti-hallucination custom instructions")

                    # Load prompt templates
                    if 'prompt_templates' in query_params:
                        templates = query_params['prompt_templates']
                        # Store the entire templates object as JSON in environment variable
                        os.environ['PROMPT_TEMPLATES'] = json.dumps(templates)
                        logger.info(f"Loaded {len(templates)} prompt templates")

                    # Load insufficient info phrases
                    if 'insufficient_info_phrases' in query_params:
                        phrases = query_params['insufficient_info_phrases']
                        if isinstance(phrases, list):
                            os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(phrases)
                            logger.info(f"Loaded {len(phrases)} insufficient info phrases")

                    # Load followup question templates
                    if 'followup_question_templates' in query_params:
                        followup_templates = query_params['followup_question_templates']
                        os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_templates)
                        logger.info(f"Loaded {len(followup_templates)} followup question templates")
    except Exception as e:
        logger.error(f"Error loading default models from file: {str(e)}")

# Function to save default models to file
def save_default_models(llm_model=None, embedding_model=None, vision_model=None, use_vision=None,
                        filter_pdf_images=None, filter_sensitivity=None, max_pdf_images=None,
                        show_filtered_images=None, use_vision_during_embedding=None,
                        temperature=None, num_ctx=None, num_predict=None, top_p=None,
                        top_k=None, repeat_penalty=None, system_prompt=None,
                        preamble=None, anti_hallucination_mode=None, anti_hallucination_custom_instructions=None,
                        prompt_templates=None, insufficient_info_phrases=None, followup_question_templates=None,
                        chunk_size=None, chunk_overlap=None, extract_tables=None, extract_images=None,
                        extract_locations=None, location_confidence_threshold=None, max_locations_per_document=None,
                        enable_geocoding=None, max_images=None, batch_size=None, processing_threads=None,
                        # New query configuration parameters
                        retrieval_k=None, relevance_threshold=None, min_documents=None, max_documents=None,
                        max_pdf_images_display=None, max_url_images_display=None, max_tables_display=None, max_pdf_links_display=None,
                        hallucination_threshold_strict=None, hallucination_threshold_balanced=None, hallucination_threshold_default=None,
                        min_statement_length=None, max_vision_context_length=None, context_truncation_strategy=None):
    try:
        # Start with existing defaults or empty dict
        defaults = {}
        if os.path.exists(DEFAULT_MODELS_FILE):
            try:
                with open(DEFAULT_MODELS_FILE, 'r') as f:
                    defaults = json.load(f)
            except:
                pass

        # Update with new values if provided
        if llm_model:
            defaults['llm_model'] = llm_model

        if embedding_model:
            defaults['embedding_model'] = embedding_model

        if vision_model:
            defaults['vision_model'] = vision_model

        # Save vision toggle state if provided
        if use_vision is not None:
            defaults['use_vision_model'] = use_vision

        # Save vision during embedding toggle state if provided
        if use_vision_during_embedding is not None:
            defaults['use_vision_model_during_embedding'] = use_vision_during_embedding

        # Save image filtering settings if provided
        if filter_pdf_images is not None:
            defaults['filter_pdf_images'] = filter_pdf_images

        if filter_sensitivity:
            defaults['filter_sensitivity'] = filter_sensitivity

        if max_pdf_images is not None:
            defaults['max_pdf_images'] = max_pdf_images

        if show_filtered_images is not None:
            defaults['show_filtered_images'] = show_filtered_images

        # Initialize model_parameters if it doesn't exist
        if 'model_parameters' not in defaults:
            defaults['model_parameters'] = {}

        # Update model parameters if provided
        if temperature is not None:
            defaults['model_parameters']['temperature'] = temperature

        if num_ctx is not None:
            defaults['model_parameters']['num_ctx'] = num_ctx

        if num_predict is not None:
            defaults['model_parameters']['num_predict'] = num_predict

        if top_p is not None:
            defaults['model_parameters']['top_p'] = top_p

        if top_k is not None:
            defaults['model_parameters']['top_k'] = top_k

        if repeat_penalty is not None:
            defaults['model_parameters']['repeat_penalty'] = repeat_penalty

        if system_prompt is not None:
            defaults['model_parameters']['system_prompt'] = system_prompt

        # Initialize query_parameters if it doesn't exist
        if 'query_parameters' not in defaults:
            defaults['query_parameters'] = {}

        # Update query parameters if provided
        if preamble is not None:
            defaults['query_parameters']['preamble'] = preamble

        # Handle anti-hallucination modes
        if anti_hallucination_mode is not None or anti_hallucination_custom_instructions is not None:
            if 'anti_hallucination_modes' not in defaults['query_parameters']:
                # Initialize with default structure if it doesn't exist
                defaults['query_parameters']['anti_hallucination_modes'] = {
                    "default_mode": "strict",
                    "available_modes": ["strict", "balanced", "off"],
                    "mode_descriptions": {
                        "strict": "Only respond with information directly found in documents",
                        "balanced": "Allow limited inference while citing sources",
                        "off": "Allow more creative responses with external knowledge"
                    }
                }

            if anti_hallucination_mode is not None:
                defaults['query_parameters']['anti_hallucination_modes']['default_mode'] = anti_hallucination_mode

            if anti_hallucination_custom_instructions is not None:
                defaults['query_parameters']['anti_hallucination_modes']['custom_instructions'] = anti_hallucination_custom_instructions

        # Update prompt templates if provided
        if prompt_templates is not None:
            defaults['query_parameters']['prompt_templates'] = prompt_templates

        # Update insufficient info phrases if provided
        if insufficient_info_phrases is not None:
            defaults['query_parameters']['insufficient_info_phrases'] = insufficient_info_phrases

        # Update followup question templates if provided
        if followup_question_templates is not None:
            defaults['query_parameters']['followup_question_templates'] = followup_question_templates

        # Update new query configuration parameters if provided
        if retrieval_k is not None:
            defaults['query_parameters']['retrieval_k'] = retrieval_k

        if relevance_threshold is not None:
            defaults['query_parameters']['relevance_threshold'] = relevance_threshold

        if min_documents is not None:
            defaults['query_parameters']['min_documents'] = min_documents

        if max_documents is not None:
            defaults['query_parameters']['max_documents'] = max_documents

        if max_pdf_images_display is not None:
            defaults['query_parameters']['max_pdf_images_display'] = max_pdf_images_display

        if max_url_images_display is not None:
            defaults['query_parameters']['max_url_images_display'] = max_url_images_display

        if max_tables_display is not None:
            defaults['query_parameters']['max_tables_display'] = max_tables_display

        if max_pdf_links_display is not None:
            defaults['query_parameters']['max_pdf_links_display'] = max_pdf_links_display

        if max_vision_context_length is not None:
            defaults['query_parameters']['max_vision_context_length'] = max_vision_context_length

        if context_truncation_strategy is not None:
            defaults['query_parameters']['context_truncation_strategy'] = context_truncation_strategy

        # Initialize hallucination_detection if it doesn't exist
        if 'hallucination_detection' not in defaults['query_parameters']:
            defaults['query_parameters']['hallucination_detection'] = {}

        # Update hallucination detection parameters if provided
        if hallucination_threshold_strict is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_strict'] = hallucination_threshold_strict

        if hallucination_threshold_balanced is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_balanced'] = hallucination_threshold_balanced

        if hallucination_threshold_default is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_default'] = hallucination_threshold_default

        if min_statement_length is not None:
            defaults['query_parameters']['hallucination_detection']['min_statement_length'] = min_statement_length

        # Initialize embedding_parameters if it doesn't exist
        if 'embedding_parameters' not in defaults:
            defaults['embedding_parameters'] = {
                "chunk_size": 800,
                "chunk_overlap": 250,
                "extract_tables": True,
                "extract_images": True,
                "extract_locations": False,
                "location_confidence_threshold": 0.5,
                "max_locations_per_document": 50,
                "enable_geocoding": True,
                "use_vision_model": False,
                "filter_sensitivity": "medium",
                "max_images": 30,
                "batch_size": 50,
                "processing_threads": 4
            }

        # Update embedding parameters if provided
        if chunk_size is not None:
            defaults['embedding_parameters']['chunk_size'] = chunk_size

        if chunk_overlap is not None:
            defaults['embedding_parameters']['chunk_overlap'] = chunk_overlap

        if extract_tables is not None:
            defaults['embedding_parameters']['extract_tables'] = extract_tables

        if extract_images is not None:
            defaults['embedding_parameters']['extract_images'] = extract_images

        if extract_locations is not None:
            defaults['embedding_parameters']['extract_locations'] = extract_locations

        if location_confidence_threshold is not None:
            defaults['embedding_parameters']['location_confidence_threshold'] = location_confidence_threshold

        if max_locations_per_document is not None:
            defaults['embedding_parameters']['max_locations_per_document'] = max_locations_per_document

        if enable_geocoding is not None:
            defaults['embedding_parameters']['enable_geocoding'] = enable_geocoding

        if use_vision_during_embedding is not None:
            defaults['embedding_parameters']['use_vision_model'] = use_vision_during_embedding

        if max_images is not None:
            defaults['embedding_parameters']['max_images'] = max_images

        if batch_size is not None:
            defaults['embedding_parameters']['batch_size'] = batch_size

        if processing_threads is not None:
            defaults['embedding_parameters']['processing_threads'] = processing_threads

        # Save to file
        with open(DEFAULT_MODELS_FILE, 'w') as f:
            json.dump(defaults, f, indent=2)

        logger.info(f"Saved default models to file: {defaults}")
        return True
    except Exception as e:
        logger.error(f"Error saving default models to file: {str(e)}")
        return False

# Function to update .env file with new model settings
def update_env_file(llm_model=None, embedding_model=None, vision_model=None):
    """Update the .env file with new model settings to maintain consistency"""
    try:
        env_file = ".env"
        if not os.path.exists(env_file):
            logger.warning(".env file not found, skipping update")
            return

        # Read current .env file
        with open(env_file, "r") as f:
            lines = f.readlines()

        # Update model settings
        updated = False
        for i, line in enumerate(lines):
            if llm_model and line.startswith("LLM_MODEL="):
                lines[i] = f"LLM_MODEL={llm_model}\n"
                updated = True
                logger.info(f"Updated .env file: LLM_MODEL={llm_model}")
            elif embedding_model and line.startswith("TEXT_EMBEDDING_MODEL="):
                lines[i] = f"TEXT_EMBEDDING_MODEL={embedding_model}\n"
                updated = True
                logger.info(f"Updated .env file: TEXT_EMBEDDING_MODEL={embedding_model}")
            elif vision_model and line.startswith("VISION_MODEL="):
                lines[i] = f"VISION_MODEL={vision_model}\n"
                updated = True
                logger.info(f"Updated .env file: VISION_MODEL={vision_model}")

        # Write back to .env file if any updates were made
        if updated:
            with open(env_file, "w") as f:
                f.writelines(lines)
            logger.info("Successfully updated .env file with new model settings")

    except Exception as e:
        logger.error(f"Error updating .env file: {str(e)}")
        raise

# Load default models from file if available
load_default_models()

# Override environment variables with values from config/config/default_models.json (configuration file takes precedence)
os.environ['LLM_MODEL'] = default_llm
logger.info(f"Setting LLM_MODEL environment variable to: {default_llm}")

os.environ['TEXT_EMBEDDING_MODEL'] = default_embedding
logger.info(f"Setting TEXT_EMBEDDING_MODEL environment variable to: {default_embedding}")

os.environ['VISION_MODEL'] = default_vision
logger.info(f"Setting VISION_MODEL environment variable to: {default_vision}")

# Enable vision model by default
if 'USE_VISION_MODEL' not in os.environ:
    os.environ['USE_VISION_MODEL'] = 'true'
    logger.info("Vision model enabled by default")

# Enable vision model during embedding by default
if 'USE_VISION_MODEL_DURING_EMBEDDING' not in os.environ:
    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true'
    logger.info("Vision model during embedding enabled by default")

# Set default model parameters if not already set
if 'LLM_TEMPERATURE' not in os.environ:
    os.environ['LLM_TEMPERATURE'] = '0.7'
    logger.info("Default LLM temperature set to 0.7")

if 'LLM_NUM_CTX' not in os.environ:
    os.environ['LLM_NUM_CTX'] = '4096'
    logger.info("Default LLM context window size set to 4096")

if 'LLM_NUM_PREDICT' not in os.environ:
    os.environ['LLM_NUM_PREDICT'] = '256'
    logger.info("Default LLM max tokens to predict set to 256")

if 'LLM_TOP_P' not in os.environ:
    os.environ['LLM_TOP_P'] = '0.9'
    logger.info("Default LLM top_p set to 0.9")

if 'LLM_TOP_K' not in os.environ:
    os.environ['LLM_TOP_K'] = '40'
    logger.info("Default LLM top_k set to 40")

if 'LLM_REPEAT_PENALTY' not in os.environ:
    os.environ['LLM_REPEAT_PENALTY'] = '1.1'
    logger.info("Default LLM repeat penalty set to 1.1")

if 'LLM_SYSTEM_PROMPT' not in os.environ:
    os.environ['LLM_SYSTEM_PROMPT'] = 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.'
    logger.info("Default LLM system prompt set")

# Set app config from environment variables
app.config['SELECTED_MODEL'] = os.environ['LLM_MODEL']
app.config['SELECTED_EMBEDDING'] = os.environ['TEXT_EMBEDDING_MODEL']
app.config['SELECTED_VISION_MODEL'] = os.environ['VISION_MODEL']
app.config['USE_VISION_MODEL'] = os.environ['USE_VISION_MODEL'].lower() == 'true'

# Set model parameters in app config
app.config['LLM_TEMPERATURE'] = float(os.environ['LLM_TEMPERATURE'])
app.config['LLM_NUM_CTX'] = int(os.environ['LLM_NUM_CTX'])
app.config['LLM_NUM_PREDICT'] = int(os.environ['LLM_NUM_PREDICT'])
app.config['LLM_TOP_P'] = float(os.environ['LLM_TOP_P'])
app.config['LLM_TOP_K'] = int(os.environ['LLM_TOP_K'])
app.config['LLM_REPEAT_PENALTY'] = float(os.environ['LLM_REPEAT_PENALTY'])
app.config['LLM_SYSTEM_PROMPT'] = os.environ['LLM_SYSTEM_PROMPT']

logger.info(f"Using LLM model: {app.config['SELECTED_MODEL']}")
logger.info(f"Using embedding model: {app.config['SELECTED_EMBEDDING']}")
logger.info(f"Using vision model: {app.config['SELECTED_VISION_MODEL']} (Enabled: {app.config['USE_VISION_MODEL']})")
logger.info(f"Model parameters: temperature={app.config['LLM_TEMPERATURE']}, top_p={app.config['LLM_TOP_P']}, " +
           f"top_k={app.config['LLM_TOP_K']}, repeat_penalty={app.config['LLM_REPEAT_PENALTY']}, " +
           f"context={app.config['LLM_NUM_CTX']}, max_tokens={app.config['LLM_NUM_PREDICT']}")

# Anti-hallucination mode: 'strict', 'balanced', or 'off'
app.config['ANTI_HALLUCINATION_MODE'] = os.getenv('ANTI_HALLUCINATION_MODE', 'strict')

# Custom Jinja2 filter for pretty-printing JSON
def prettyjson(data):
    return json.dumps(data, indent=2, ensure_ascii=False)
app.jinja_env.filters['prettyjson'] = prettyjson

# Custom Jinja2 filter for markdown rendering
def markdown_filter(text):
    import markdown
    if text is None:
        return ""
    return markdown.markdown(text, extensions=['extra', 'nl2br'])
app.jinja_env.filters['markdown'] = markdown_filter

# Context processor to add common variables to all templates
@app.context_processor
def inject_common_variables():
    from datetime import datetime
    from flask_wtf.csrf import generate_csrf

    def safe_has_permission(function_name):
        """
        Safely check if the current user has a dashboard permission.
        Returns False if user is not authenticated or doesn't have the permission.
        """
        try:
            if not current_user.is_authenticated:
                return False
            return current_user.has_dashboard_permission(function_name)
        except AttributeError:
            # Handle case where current_user is AnonymousUserMixin
            return False
        except Exception:
            # Handle any other unexpected errors
            return False

    def csrf_token():
        """Generate CSRF token for templates."""
        try:
            return generate_csrf()
        except Exception:
            return ""

    return {
        'now': datetime.now(),
        'safe_has_permission': safe_has_permission,
        'csrf_token': csrf_token
    }






@app.route('/admin/upload', methods=['GET', 'POST'])
@admin_required
@function_permission_required('upload_content')
def upload_file():
    if request.method == 'POST':
        category = request.form.get('category')
        files = request.files.getlist('file')
        url = request.form.get('url')

        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.accept_mimetypes['application/json'] > 0

        if not category:
            if is_ajax:
                return jsonify({'success': False, 'error': 'Category is required.'}), 400
            flash("Category is required.", "error")
            return redirect(url_for('upload_file'))

        valid_files = [f for f in files if f and getattr(f, 'filename', None)]
        if valid_files and valid_files[0].filename:
            results = []
            batch_filenames = set()
            batch_hashes = set()
            for idx, file in enumerate(valid_files):
                if not (file and file.filename and utils.allowed_file(str(file.filename))):
                    results.append({'file': getattr(file, 'filename', 'Unknown'), 'success': False, 'message': 'Invalid file type.', 'original_filename': getattr(file, 'filename', 'Unknown'), 'system_filename': None, 'input_index': idx})
                    continue
                pdf_url = request.form.get('pdf_url')
                original_filename = secure_filename(file.filename)
                # Calculate file hash for in-batch duplicate detection
                file.seek(0)
                file_hash = utils.calculate_file_hash(file)
                file.seek(0)
                # In-batch duplicate detection
                if original_filename in batch_filenames or (file_hash and file_hash in batch_hashes):
                    results.append({'file': file.filename, 'success': False, 'message': 'Duplicate in batch upload', 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                    continue
                batch_filenames.add(original_filename)
                if file_hash:
                    batch_hashes.add(file_hash)
                is_duplicate, duplicate_info = check_duplicate_pdf(file, category)
                duplicate_action = request.form.get('duplicate_action', 'reject')
                if is_duplicate:
                    duplicate_filename = duplicate_info['filename']
                    match_type = duplicate_info['type']
                    logger.info(f"Duplicate PDF detected: '{file.filename}' matches existing file '{duplicate_filename}' by {match_type}.")
                    success, delete_message = delete_file(category, duplicate_filename)
                    if not success:
                        results.append({'file': file.filename, 'success': False, 'message': f"Error replacing existing file: {delete_message}", 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                        continue
                use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
                filter_sensitivity = request.form.get('filter_sensitivity')
                if not filter_sensitivity or filter_sensitivity not in ['low', 'medium', 'high']:
                    filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')
                max_images = request.form.get('max_images')
                if max_images:
                    try:
                        max_images = int(max_images)
                        if max_images < 1:
                            max_images = 10
                        elif max_images > 50:
                            max_images = 50
                    except (ValueError, TypeError):
                        max_images = None
                force_update = request.form.get('force_update', 'false').lower() == 'true'
                form_id_str = request.form.get('gated_form_id')
                if form_id_str and form_id_str.isdigit():
                    form_id = int(form_id_str)
                    duplicate_action = request.form.get('duplicate_action', 'reject')
                    if is_duplicate and duplicate_action == 'replace':
                        duplicate_filename = duplicate_info['filename']
                        logger.info(f"Replacing existing gated PDF: '{duplicate_filename}' with '{file.filename}'")
                        success, delete_message = delete_file(category, duplicate_filename)
                        if not success:
                            results.append({'file': file.filename, 'success': False, 'message': f"Error replacing existing gated PDF: {delete_message}", 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                            continue
                    elif is_duplicate and duplicate_action == 'reject':
                        results.append({'file': file.filename, 'success': False, 'message': 'Upload rejected (duplicate)', 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                        continue
                    convert_to_non_ocr = request.form.get('convert_to_non_ocr', 'false').lower() == 'true'
                    keep_only_non_ocr = request.form.get('keep_only_non_ocr', 'false').lower() == 'true'
                    conversion_dpi = 300
                    try:
                        conversion_dpi = int(request.form.get('conversion_dpi', 300))
                        if conversion_dpi < 150:
                            conversion_dpi = 150
                        elif conversion_dpi > 600:
                            conversion_dpi = 600
                    except (ValueError, TypeError):
                        conversion_dpi = 300
                    success, message = upload_gated_pdf(
                        file,
                        category,
                        form_id,
                        source_url=pdf_url,
                        convert_to_non_ocr=convert_to_non_ocr,
                        conversion_dpi=conversion_dpi,
                        keep_only_non_ocr=keep_only_non_ocr
                    )
                    if success:
                        results.append({'file': file.filename, 'success': True, 'message': f"Gated PDF uploaded. {message}", 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                    else:
                        results.append({'file': file.filename, 'success': False, 'message': f"Failed to upload gated PDF. {message}", 'original_filename': file.filename, 'system_filename': file.filename, 'input_index': idx})
                else:
                    success, message = embed_file_db_first(
                        file,
                        category,
                        source_url=pdf_url,
                        use_vision=use_vision,
                        filter_sensitivity=filter_sensitivity,
                        max_images=max_images,
                        force_update=force_update
                    )
                    # Use the generated filename if available
                    system_filename = getattr(file, 'filename', None)
                    if success:
                        results.append({'file': file.filename, 'success': True, 'message': 'Uploaded successfully.', 'original_filename': file.filename, 'system_filename': system_filename, 'input_index': idx})
                    else:
                        results.append({'file': file.filename, 'success': False, 'message': message, 'original_filename': file.filename, 'system_filename': system_filename, 'input_index': idx})
            if is_ajax:
                return jsonify({'results': results}), 200
            for msg in results:
                flash(f"{msg['file']}: {msg['message']}", "info" if msg['success'] else "error")
            return redirect(url_for('upload_file'))

        if url:
            # Get the depth parameter from the form (default to 0 if not provided)
            try:
                depth = int(request.form.get('depth', 0))
                # Limit depth to a reasonable range
                if depth < 0:
                    depth = 0
                if depth > 3:
                    depth = 3
            except (ValueError, TypeError):
                depth = 0

            # Get force update parameter
            force_update = request.form.get('force_update', 'false').lower() == 'true'

            logger.info(f"Scraping URL {url} with depth {depth}, force_update={force_update}")

            # Use database-first URL scraping
            success, message, scraped_data = scrape_url_db_first(url, category, depth, force_update)

            if success and scraped_data:
                dest_dir = os.path.join(TEMP_FOLDER, category)
                os.makedirs(dest_dir, exist_ok=True)
                filename = secure_filename(url.replace('https://', '').replace('http://', '').replace('/', '_')) + '.txt'
                dest = os.path.join(dest_dir, filename)

                # Import required classes
                from langchain.schema import Document
                from app.services.text_splitter_service import get_text_splitter

                # Use centralized text splitter with standardized chunk size
                splitter = get_text_splitter("web")

                # Get the database
                db = get_vector_db(category)

                # Initialize counters
                total_chunks = 0
                pages_processed = 0

                # Log database retrieval info
                if scraped_data.get("database_retrieval"):
                    logger.info(f"Using database content for URL {url}, last scraped: {scraped_data.get('url_last_scraped')}")

                # Check if we have individual pages
                if scraped_data.get("pages"):
                    # Process each page individually to avoid large batches
                    all_page_texts = []

                    for page in scraped_data["pages"]:
                        page_text = page["text"]
                        page_url = page["url"]
                        page_depth = page["depth"]

                        # Add to the combined text file
                        all_page_texts.append(page_text)

                        # Create metadata for this page
                        metadata = {
                            "source": filename,
                            "original_url": url,
                            "page_url": page_url,
                            "type": "url",
                            "scrape_depth": depth,
                            "page_depth": page_depth,
                            "pages_scraped": scraped_data.get("pages_scraped", 1),
                            "max_depth_reached": scraped_data.get("max_depth_reached", 0)
                        }

                        # Add images and links to metadata
                        if scraped_data.get("images"):
                            metadata["images"] = json.dumps(scraped_data["images"])

                        if scraped_data.get("links"):
                            metadata["pdf_links"] = json.dumps(scraped_data["links"])

                        # Create document and split into smaller chunks
                        page_doc = Document(page_content=page_text, metadata=metadata)
                        page_chunks = splitter.split_documents([page_doc])

                        # Process chunks in smaller batches to avoid memory issues
                        BATCH_SIZE = 10
                        for i in range(0, len(page_chunks), BATCH_SIZE):
                            batch = page_chunks[i:i+BATCH_SIZE]
                            # Add to vector database
                            db.add_documents(batch)
                            total_chunks += len(batch)

                        pages_processed += 1
                        logger.info(f"Processed page {pages_processed}/{len(scraped_data['pages'])} with {len(page_chunks)} chunks")

                    # Save the combined text to a file
                    combined_text = "\n\n".join(all_page_texts)
                    with open(dest, 'w', encoding='utf-8') as f:
                        f.write(combined_text)

                else:
                    # Fallback for old format (should not happen with new code)
                    logger.warning("Using fallback processing for URL scraping - no pages found in scraped data")

                    # Get text content (fallback)
                    scraped_text = scraped_data.get("text", "No content extracted")

                    # Save the text content to a file
                    with open(dest, 'w', encoding='utf-8') as f:
                        f.write(scraped_text)

                    # Create basic metadata
                    metadata = {
                        "source": filename,
                        "original_url": url,
                        "type": "url",
                        "scrape_depth": depth
                    }

                    # Add additional metadata if available
                    if scraped_data.get("pages_scraped"):
                        metadata["pages_scraped"] = scraped_data["pages_scraped"]

                    if scraped_data.get("max_depth_reached"):
                        metadata["max_depth_reached"] = scraped_data["max_depth_reached"]

                    if scraped_data.get("images"):
                        metadata["images"] = json.dumps(scraped_data["images"])

                    if scraped_data.get("links"):
                        metadata["pdf_links"] = json.dumps(scraped_data["links"])

                    # Create document and split into smaller chunks
                    doc = Document(page_content=scraped_text, metadata=metadata)
                    chunks = splitter.split_documents([doc])

                    # Process chunks in smaller batches
                    BATCH_SIZE = 10
                    for i in range(0, len(chunks), BATCH_SIZE):
                        batch = chunks[i:i+BATCH_SIZE]
                        # Add to vector database
                        db.add_documents(batch)
                        total_chunks += len(batch)

                # Save record to database
                db_utils.save_scraped_page(category, url, filename)

                # Create success message
                pages_info = f" from {scraped_data.get('pages_scraped', 1)} pages" if depth > 0 else ""

                # Add database retrieval info to success message
                if scraped_data.get("database_retrieval"):
                    flash(f"URL {url} retrieved from database and embedded successfully with {total_chunks} chunks{pages_info}.", "success")
                else:
                    flash(f"URL {url} scraped and embedded successfully with {total_chunks} chunks{pages_info}.", "success")

                # Log detailed information
                logger.info(f"Successfully embedded URL {url} with depth {depth}, " +
                           f"processed {scraped_data.get('pages_scraped', 1)} pages, " +
                           f"created {total_chunks} chunks")
            else:
                # Display the error message from the scrape_url_db_first function
                flash(message, "error")

        return redirect(url_for('upload_file'))
    
    # Get form_id from query parameter for pre-selection
    selected_form_id = request.args.get('form_id')
    
    return render_template('upload.html', 
                         categories=list_categories(), 
                         page_title="Upload Content", 
                         forms=forms_db.get_all_forms(),
                         selected_form_id=selected_form_id)

@app.route('/admin/categories', methods=['GET', 'POST', 'DELETE'])
def list_categories_route():
    # Handle POST request for creating a new category
    if request.method == 'POST':
        logger.info(f"POST request to /admin/categories received. Content-Type: {request.content_type}")

        # Check if the request is JSON or form data
        if request.is_json:
            logger.info(f"JSON data received: {request.json}")
            data = request.json
            category = data.get('category')
        else:
            logger.info(f"Form data received: {request.form}")
            category = request.form.get('category')

        if category:
            logger.info(f"Creating category: {category}")
            dest_dir = os.path.join(TEMP_FOLDER, category)
            os.makedirs(dest_dir, exist_ok=True)

            # Return appropriate response based on request type
            if request.is_json:
                logger.info(f"Category {category} created successfully (JSON response)")
                return jsonify({"message": f"Category {category} created successfully."}), 200
            else:
                flash(f"Category {category} created successfully.", "success")
                logger.info(f"Category {category} created successfully (redirect response)")
                return redirect(url_for('admin.admin_dashboard'))
        else:
            logger.warning("Category name is required but was not provided")

            # Return appropriate error response based on request type
            if request.is_json:
                return jsonify({"error": "Category name is required."}), 400
            else:
                flash("Category name is required.", "error")
                return redirect(url_for('admin.admin_dashboard'))

    # Handle DELETE request for deleting a category
    if request.method == 'DELETE':
        logger.info(f"DELETE request to /admin/categories received. Args: {request.args}")
        category = request.args.get('category')
        if category:
            logger.info(f"Deleting category: {category}")
            import shutil
            resources_deleted = []
            errors = []

            try:
                # Delete main files directory
                dest_dir = os.path.join(TEMP_FOLDER, category)
                if os.path.exists(dest_dir):
                    shutil.rmtree(dest_dir)
                    resources_deleted.append("files")
                    logger.info(f"Deleted files directory for category: {category}")

                # Delete images directory
                IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./data/temp/pdf_images")
                images_dir = os.path.join(IMAGES_FOLDER, category)
                if os.path.exists(images_dir):
                    shutil.rmtree(images_dir)
                    resources_deleted.append("extracted images")
                    logger.info(f"Deleted images directory for category: {category}")

                # Delete tables directory
                TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./data/temp/pdf_tables")
                tables_dir = os.path.join(TABLES_FOLDER, category)
                if os.path.exists(tables_dir):
                    shutil.rmtree(tables_dir)
                    resources_deleted.append("extracted tables")
                    logger.info(f"Deleted tables directory for category: {category}")

                # Delete database entries
                try:
                    # This assumes there's a function to delete all entries for a category
                    # If it doesn't exist, you might need to implement it
                    db_utils.delete_category_scraped_pages(category)
                    resources_deleted.append("database entries")
                    logger.info(f"Deleted database entries for category: {category}")
                except Exception as db_err:
                    error_msg = f"Failed to delete database entries for category {category}: {str(db_err)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                # Prepare success message
                if resources_deleted:
                    success_message = f"Category {category} deleted successfully with all {', '.join(resources_deleted)}"
                    if errors:
                        success_message += f" (with {len(errors)} errors)"
                    logger.info(success_message)
                    return jsonify({"message": success_message}), 200
                else:
                    message = f"Category {category} not found or already deleted"
                    logger.warning(message)
                    return jsonify({"message": message}), 200

            except Exception as e:
                error_msg = f"Error deleting category {category}: {str(e)}"
                logger.error(error_msg)
                if errors:
                    error_msg += f" Additional errors: {'; '.join(errors)}"
                return jsonify({"error": error_msg}), 500
        else:
            logger.warning("Category name is required for deletion but was not provided")
            return jsonify({"error": "Category name is required."}), 400

    # Handle GET request - redirect to admin dashboard
    return redirect(url_for('admin.admin_dashboard'))

# Route removed - functionality moved to list_categories_route

@app.route('/admin/categories/update/<old_category>', methods=['POST'])
def update_category(old_category):
    new_category = request.form.get('new_category')
    if new_category and old_category != new_category:
        old_dest_dir = os.path.join(TEMP_FOLDER, old_category)
        new_dest_dir = os.path.join(TEMP_FOLDER, new_category)
        if os.path.exists(old_dest_dir):
            os.rename(old_dest_dir, new_dest_dir)
        flash(f"Category {old_category} updated to {new_category}.", "success")
    else:
        flash("Invalid category name.", "error")
    return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/categories/delete/<category>', methods=['POST'])
def delete_category(category):
    """
    Delete a category and all its associated resources (files, images, tables).
    """
    import shutil
    resources_deleted = []
    errors = []

    try:
        # Delete main files directory
        dest_dir = os.path.join(TEMP_FOLDER, category)
        if os.path.exists(dest_dir):
            shutil.rmtree(dest_dir)
            resources_deleted.append("files")
            logger.info(f"Deleted files directory for category: {category}")

        # Delete images directory
        IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./data/temp/pdf_images")
        images_dir = os.path.join(IMAGES_FOLDER, category)
        if os.path.exists(images_dir):
            shutil.rmtree(images_dir)
            resources_deleted.append("extracted images")
            logger.info(f"Deleted images directory for category: {category}")

        # Delete tables directory
        TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./data/temp/pdf_tables")
        tables_dir = os.path.join(TABLES_FOLDER, category)
        if os.path.exists(tables_dir):
            shutil.rmtree(tables_dir)
            resources_deleted.append("extracted tables")
            logger.info(f"Deleted tables directory for category: {category}")

        # Delete database entries
        try:
            # This assumes there's a function to delete all entries for a category
            # If it doesn't exist, you might need to implement it
            db_utils.delete_category_scraped_pages(category)
            resources_deleted.append("database entries")
            logger.info(f"Deleted database entries for category: {category}")
        except Exception as db_err:
            error_msg = f"Failed to delete database entries for category {category}: {str(db_err)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # Prepare success message
        if resources_deleted:
            success_message = f"Category {category} deleted successfully with all {', '.join(resources_deleted)}"
            if errors:
                success_message += f" (with {len(errors)} errors)"
            flash(success_message, "success")
        else:
            flash(f"Category {category} not found or already deleted.", "warning")

    except Exception as e:
        error_msg = f"Failed to delete category {category}: {str(e)}"
        logger.error(error_msg)
        if errors:
            error_msg += f" Additional errors: {'; '.join(errors)}"
        flash(error_msg, "error")

    return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/files')
@admin_required
@function_permission_required('manage_files')
def list_files():
    """
    List files using database as primary source (fixed for duplicate detection)
    """
    files_data = {}

    try:
        # Get files from database first
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')

        db_records = cursor.fetchall()
        conn.close()

        logger.info(f"Found {len(db_records)} files in database")

        # Group by category
        db_files_by_category = {}
        for record in db_records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record

            if category not in db_files_by_category:
                db_files_by_category[category] = []

            db_files_by_category[category].append({
                'id': pdf_id,
                'filename': filename,
                'original_filename': original_filename,
                'category': category,
                'created_at': created_at,
                'file_size': file_size,
                'page_count': page_count
            })

        # Process each category from database
        for category, db_file_list in db_files_by_category.items():
            files = []

            for db_file in db_file_list:
                filename = db_file['filename']
                original_filename = db_file['original_filename']

                # Find filesystem path
                fs_path = None
                possible_paths = [
                    # New structure with subdirectories
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),

                    # Legacy structure - direct files
                    os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, filename),
                    os.path.join(TEMP_FOLDER, '_temp', category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, filename),
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        fs_path = path
                        break

                if fs_path:
                    # Get vector metadata
                    try:
                        db = get_vector_db(category)
                        source_filename = os.path.basename(fs_path)
                        docs = db.similarity_search_with_score("", k=1, filter={"source": source_filename})

                        vector_metadata = {}
                        if docs and len(docs) > 0:
                            doc, _ = docs[0]
                            vector_metadata = {
                                'original_url': doc.metadata.get("original_url"),
                                'source_url_id': doc.metadata.get("source_url_id"),
                                'pdf_document_id': doc.metadata.get("pdf_document_id"),
                                'cover_image_id': doc.metadata.get("cover_image_id"),
                                'database_retrieval': doc.metadata.get("database_retrieval", False)
                            }
                    except Exception as e:
                        logger.warning(f"Could not get vector metadata for {category}/{filename}: {e}")
                        vector_metadata = {}

                    # Create file data structure
                    file_data = {
                        "original_filename": original_filename,
                        "source": os.path.basename(fs_path),
                        "type": "pdf",
                        "database_id": db_file['id'],
                        "created_at": db_file['created_at'],
                        "file_size": db_file['file_size'],
                        "page_count": db_file['page_count']
                    }

                    # Add vector metadata
                    for key, value in vector_metadata.items():
                        if value:
                            file_data[key] = value

                    files.append(file_data)
                    logger.debug(f"Added database file: {category}/{original_filename}")
                else:
                    logger.warning(f"Database file not found in filesystem: {category}/{filename}")

            if files:
                files_data[category] = files

        # Add URL files from scraped pages
        scraped_pages = db_utils.get_scraped_pages()
        for page in scraped_pages:
            category = page['category']
            if category not in files_data:
                files_data[category] = []

            url_file_data = {
                "original_filename": page['url'],
                "source": page['filename'],
                "type": "url",
                "scrape_depth": page.get('scrape_depth', 0),
                "pages_scraped": page.get('pages_scraped', 0)
            }
            files_data[category].append(url_file_data)

    except Exception as e:
        logger.error(f"Error in database-first list_files: {e}")
        flash(f"Error loading files: {str(e)}", "error")
        files_data = {}

    logger.info(f"files_data type: {type(files_data)}, content: {files_data}")
    if not isinstance(files_data, dict):
        raise ValueError("files_data is not a dictionary")
    return render_template('files.html', files_data=files_data)


                            if docs and len(docs) > 0:
                                doc, _ = docs[0]
                                if doc.metadata.get("scrape_depth"):
                                    file_data["scrape_depth"] = doc.metadata.get("scrape_depth")
                                if doc.metadata.get("pages_scraped"):
                                    file_data["pages_scraped"] = doc.metadata.get("pages_scraped")
                                if doc.metadata.get("source_url_id"):
                                    file_data["source_url_id"] = doc.metadata.get("source_url_id")
                                if doc.metadata.get("database_retrieval"):
                                    file_data["database_retrieval"] = doc.metadata.get("database_retrieval")
                                if doc.metadata.get("url_last_scraped"):
                                    file_data["url_last_scraped"] = doc.metadata.get("url_last_scraped")

                            files.append(file_data)
                            added_sources.add(filename)  # Mark this source as added

                # Now, check for files in the new hierarchical structure
                for pdf_dir_name in os.listdir(category_path):
                    pdf_dir_path = os.path.join(category_path, pdf_dir_name)

                    # Check if it's a directory (part of the new structure)
                    if os.path.isdir(pdf_dir_path):
                        # Look for PDF files in this directory
                        for filename in os.listdir(pdf_dir_path):
                            if filename.endswith('.pdf'):
                                # Skip if we've already added this source filename
                                if filename in added_sources:
                                    logger.info(f"Skipping duplicate file: {filename}")
                                    continue
                                
                                # Get the full path to the PDF file
                                pdf_path = os.path.join(pdf_dir_path, filename)

                                # Only process if it's a file
                                if os.path.isfile(pdf_path):
                                    # Calculate the original filename properly for both ocr_ and non_ocr_ prefixes
                                    if filename.startswith('non_ocr_'):
                                        original_filename = filename[8:]  # Remove 'non_ocr_' prefix
                                    elif filename.startswith('ocr_'):
                                        original_filename = filename[4:]  # Remove 'ocr_' prefix
                                    else:
                                        original_filename = filename.split('_', 1)[1] if '_' in filename else filename
                                
                                # Get the full path to the PDF file
                                pdf_path = os.path.join(pdf_dir_path, filename)

                                # Only process if it's a file
                                if os.path.isfile(pdf_path):
                                    # Calculate the original filename properly for both ocr_ and non_ocr_ prefixes
                                    if filename.startswith('non_ocr_'):
                                        original_filename = filename[8:]  # Remove 'non_ocr_' prefix
                                    elif filename.startswith('ocr_'):
                                        original_filename = filename[4:]  # Remove 'ocr_' prefix
                                    else:
                                        original_filename = filename.split('_', 1)[1] if '_' in filename else filename

                                    # Get the vector data to check for original_url and database info
                                    # Try multiple search strategies to find metadata
                                    db = get_vector_db(category)
                                    docs = []
                                    
                                    # Strategy 1: Search with exact filename
                                    try:
                                        docs = db.similarity_search_with_score("", k=1, filter={"source": filename})
                                    except:
                                        pass
                                    
                                    # Strategy 2: If no results and this is a non-OCR file, try searching for the original OCR filename
                                    if not docs and filename.startswith('non_ocr_'):
                                        original_ocr_filename = filename.replace('non_ocr_', 'ocr_', 1)
                                        try:
                                            docs = db.similarity_search_with_score("", k=1, filter={"source": original_ocr_filename})
                                            logger.info(f"Found metadata for {filename} using original OCR filename: {original_ocr_filename}")
                                        except:
                                            pass
                                    
                                    # Strategy 3: If still no results, try searching without prefix
                                    if not docs:
                                        base_filename = filename
                                        if filename.startswith('ocr_'):
                                            base_filename = filename[4:]  # Remove 'ocr_' prefix
                                        elif filename.startswith('non_ocr_'):
                                            base_filename = filename[8:]  # Remove 'non_ocr_' prefix
                                        
                                        try:
                                            # Search for any filename containing the base name
                                            all_docs = db.similarity_search_with_score("", k=100, filter={"category": category})
                                            for doc, score in all_docs:
                                                if base_filename in doc.metadata.get("source", ""):
                                                    docs = [(doc, score)]
                                                    logger.info(f"Found metadata for {filename} using base filename search: {doc.metadata.get('source')}")
                                                    break
                                        except:
                                            pass
                                    
                                    original_url = None
                                    source_url_id = None
                                    pdf_document_id = None
                                    cover_image_id = None
                                    database_retrieval = False

                                    # Extract metadata if it exists
                                    if docs and len(docs) > 0:
                                        doc, _ = docs[0]
                                        if doc.metadata.get("original_url"):
                                            original_url = doc.metadata.get("original_url")
                                        if doc.metadata.get("source_url_id"):
                                            source_url_id = doc.metadata.get("source_url_id")
                                        if doc.metadata.get("pdf_document_id"):
                                            pdf_document_id = doc.metadata.get("pdf_document_id")
                                        if doc.metadata.get("cover_image_id"):
                                            cover_image_id = doc.metadata.get("cover_image_id")
                                        if doc.metadata.get("database_retrieval"):
                                            database_retrieval = doc.metadata.get("database_retrieval")

                                    file_data = {
                                        "original_filename": original_filename,
                                        "source": filename,  # Use the actual filename on disk
                                        "type": "pdf"
                                    }

                                    if original_url:
                                        file_data["original_url"] = original_url

                                    # Add database info
                                    if source_url_id:
                                        file_data["source_url_id"] = source_url_id
                                    if pdf_document_id:
                                        file_data["pdf_document_id"] = pdf_document_id
                                    if cover_image_id:
                                        file_data["cover_image_id"] = cover_image_id
                                    if database_retrieval:
                                        file_data["database_retrieval"] = database_retrieval

                                    files.append(file_data)
                                    added_sources.add(filename)  # Mark this source as added

        if files:
            files_data[category] = files

    logger.info(f"files_data type: {type(files_data)}, content: {files_data}")
    if not isinstance(files_data, dict):
        raise ValueError("files_data is not a dictionary")
    return render_template('files.html', files_data=files_data)

@app.route('/admin/delete/<category>/<filename>', methods=['POST', 'DELETE'])
@admin_required
def delete_file_route(category, filename):
    """
    Delete a file and all its associated resources (images, tables, vector embeddings).

    This route handler calls the delete_file function and also removes any database
    entries for scraped pages if applicable.
    """
    try:
        # ALWAYS attempt to delete database records first (to prevent orphaned records)
        database_deletion_success = False
        database_deleted_count = 0
        try:
            database_deleted_count = db_utils.delete_pdf_document_records(category, filename)
            database_deletion_success = True
            if database_deleted_count > 0:
                logger.info(f"Deleted {database_deleted_count} PDF document database records for {category}/{filename}")
        except Exception as db_err:
            logger.warning(f"Failed to delete PDF document database records for {category}/{filename}: {str(db_err)}")

        # Delete scraped page entry if exists
        scraped_page_success = True
        try:
            db_utils.delete_scraped_page(category, filename)
            logger.info(f"Deleted scraped page database entry for {category}/{filename}")
        except Exception as db_err:
            logger.warning(f"Failed to delete scraped page database entry for {category}/{filename}: {str(db_err)}")
            scraped_page_success = False

        # Call the enhanced delete_file function that handles cleanup of associated resources
        logger.info(f"Calling delete_file for {category}/{filename}")
        try:
            filesystem_success, filesystem_message = delete_file(category, filename)
            logger.info(f"delete_file returned: success={filesystem_success}, message='{filesystem_message}'")
        except Exception as fs_err:
            logger.error(f"delete_file threw an exception: {str(fs_err)}")
            import traceback
            logger.error(f"delete_file traceback: {traceback.format_exc()}")
            filesystem_success = False
            filesystem_message = f"delete_file exception: {str(fs_err)}"

        # Always clean up orphaned locations after PDF deletion
        try:
            from app.utils.database import cleanup_orphaned_locations
            cleanup_result = cleanup_orphaned_locations()
            logger.info(f"Orphaned location cleanup after PDF delete: {cleanup_result}")
        except Exception as cleanup_err:
            logger.warning(f"Failed to cleanup orphaned locations after PDF delete: {str(cleanup_err)}")
            cleanup_result = None

        # Determine overall success and build comprehensive message
        if filesystem_success:
            message = filesystem_message
            if database_deleted_count > 0:
                message += f"; removed {database_deleted_count} database records"
            if cleanup_result:
                message += f"; cleaned up {cleanup_result.get('locations_deleted', 0)} orphaned locations"
            if not scraped_page_success:
                message += "; failed to delete scraped page entry"
            success = True
        elif database_deletion_success and database_deleted_count > 0:
            # Even if filesystem deletion failed, if we cleaned up database records, that's partial success
            message = f"Cleaned up {database_deleted_count} orphaned database records"
            if cleanup_result:
                message += f"; cleaned up {cleanup_result.get('locations_deleted', 0)} orphaned locations"
            if filesystem_message:
                message += f" (filesystem deletion failed: {filesystem_message})"
            success = True  # Consider this a success to prevent orphaned records
        else:
            message = filesystem_message or "Failed to delete file"
            if not database_deletion_success:
                message += "; Failed to delete database records"
            success = False

        # Return appropriate response based on request method
        if success:
            if request.method == 'DELETE':
                logger.info(f"File {filename} deleted successfully: {message}")
                return jsonify({"message": message}), 200
            else:
                flash(message, "success")
        else:
            if request.method == 'DELETE':
                logger.warning(f"Failed to delete file {filename}: {message}")
                return jsonify({"error": message}), 400
            else:
                flash(message, "error")
    except Exception as e:
        error_msg = f"Failed to delete file: {str(e)}"
        logger.error(error_msg)
        if request.method == 'DELETE':
            return jsonify({"error": error_msg}), 500
        else:
            flash(error_msg, "error")

    # Only redirect for POST requests
    return redirect(url_for('list_files'))

@app.route('/admin/vector_data/<category>/<filename>')
def view_vector_data(category, filename):
    try:
        db = get_vector_db(category)
        docs = db.similarity_search_with_score("", k=100, filter={"source": filename})
        vector_data = []
        for doc, score in docs:
            if doc.metadata.get("source") == filename:
                vector_data.append({"content": doc.page_content, "metadata": doc.metadata, "score": score})
        return render_template('vector_data.html', category=category, filename=filename, vector_data=vector_data)
    except Exception as e:
        flash(f"Failed to retrieve vector data: {str(e)}", "error")
        return redirect(url_for('list_files'))

@app.route('/admin/unified_config', methods=['GET', 'POST'])
@admin_required
@function_permission_required('model_settings')
def unified_config():
    """Page and endpoint for model settings"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Check which tab is being saved
            tab = data.get('tab', 'all')

            # Initialize variables with None to avoid errors
            llm_model = embedding_model = vision_model = use_vision = None
            use_vision_during_embedding = filter_sensitivity = max_pdf_images = show_filtered_images = None
            preamble = anti_hallucination_mode = anti_hallucination_custom_instructions = None
            prompt_templates = insufficient_info_phrases = followup_question_templates = None
            chunk_size = chunk_overlap = extract_tables = extract_images = None
            extract_locations = location_confidence_threshold = max_locations_per_document = enable_geocoding = None
            batch_size = processing_threads = None

            # New query configuration parameters
            retrieval_k = relevance_threshold = min_documents = max_documents = None
            max_pdf_images_display = max_url_images_display = max_tables_display = max_pdf_links_display = None
            hallucination_threshold_strict = hallucination_threshold_balanced = hallucination_threshold_default = None
            min_statement_length = max_vision_context_length = context_truncation_strategy = None

            # Extract data based on which tab is being saved
            if tab in ['all', 'models']:
                # AI Models
                llm_model = data.get('llm_model')
                embedding_model = data.get('embedding_model')
                vision_model = data.get('vision_model')
                use_vision = data.get('use_vision')

            if tab in ['all', 'query']:
                # Query Configuration
                preamble = data.get('preamble')
                anti_hallucination_mode = data.get('anti_hallucination_mode')
                anti_hallucination_custom_instructions = data.get('anti_hallucination_custom_instructions')
                prompt_templates = data.get('prompt_templates')
                insufficient_info_phrases = data.get('insufficient_info_phrases')
                followup_question_templates = data.get('followup_question_templates')

                # New query configuration parameters
                retrieval_k = data.get('retrieval_k')
                relevance_threshold = data.get('relevance_threshold')
                min_documents = data.get('min_documents')
                max_documents = data.get('max_documents')
                max_pdf_images_display = data.get('max_pdf_images_display')
                max_url_images_display = data.get('max_url_images_display')
                max_tables_display = data.get('max_tables_display')
                max_pdf_links_display = data.get('max_pdf_links_display')
                hallucination_threshold_strict = data.get('hallucination_threshold_strict')
                hallucination_threshold_balanced = data.get('hallucination_threshold_balanced')
                hallucination_threshold_default = data.get('hallucination_threshold_default')
                min_statement_length = data.get('min_statement_length')
                max_vision_context_length = data.get('max_vision_context_length')
                context_truncation_strategy = data.get('context_truncation_strategy')

            if tab in ['all', 'embedding']:
                # Embedding Configuration
                chunk_size = data.get('chunk_size')
                chunk_overlap = data.get('chunk_overlap')
                extract_tables = data.get('extract_tables')
                extract_images = data.get('extract_images')
                extract_locations = data.get('extract_locations')
                location_confidence_threshold = data.get('location_confidence_threshold')
                max_locations_per_document = data.get('max_locations_per_document')
                enable_geocoding = data.get('enable_geocoding')
                batch_size = data.get('batch_size')
                processing_threads = data.get('processing_threads')

                # Vision settings for embedding (these are part of the embedding tab)
                if tab == 'embedding':
                    use_vision_during_embedding = data.get('use_vision_during_embedding')
                    filter_sensitivity = data.get('filter_sensitivity')
                    max_pdf_images = data.get('max_pdf_images')
                    show_filtered_images = data.get('show_filtered_images')

            # Save all configuration parameters
            success = save_default_models(
                # AI Models
                llm_model=llm_model,
                embedding_model=embedding_model,
                vision_model=vision_model,
                use_vision=use_vision,
                use_vision_during_embedding=use_vision_during_embedding,
                filter_sensitivity=filter_sensitivity,
                max_pdf_images=max_pdf_images,
                show_filtered_images=show_filtered_images,

                # Query Configuration
                preamble=preamble,
                anti_hallucination_mode=anti_hallucination_mode,
                anti_hallucination_custom_instructions=anti_hallucination_custom_instructions,
                prompt_templates=prompt_templates,
                insufficient_info_phrases=insufficient_info_phrases,
                followup_question_templates=followup_question_templates,

                # New query configuration parameters
                retrieval_k=retrieval_k,
                relevance_threshold=relevance_threshold,
                min_documents=min_documents,
                max_documents=max_documents,
                max_pdf_images_display=max_pdf_images_display,
                max_url_images_display=max_url_images_display,
                max_tables_display=max_tables_display,
                max_pdf_links_display=max_pdf_links_display,
                hallucination_threshold_strict=hallucination_threshold_strict,
                hallucination_threshold_balanced=hallucination_threshold_balanced,
                hallucination_threshold_default=hallucination_threshold_default,
                min_statement_length=min_statement_length,
                max_vision_context_length=max_vision_context_length,
                context_truncation_strategy=context_truncation_strategy,

                # Embedding Configuration
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                extract_tables=extract_tables,
                extract_images=extract_images,
                extract_locations=extract_locations,
                location_confidence_threshold=location_confidence_threshold,
                max_locations_per_document=max_locations_per_document,
                enable_geocoding=enable_geocoding,
                batch_size=batch_size,
                processing_threads=processing_threads
            )

            if success:
                # Update environment variables
                # AI Models
                if llm_model:
                    app.config['SELECTED_MODEL'] = llm_model
                    os.environ['LLM_MODEL'] = llm_model  # Fix: Use LLM_MODEL instead of SELECTED_MODEL
                    logger.info(f"Updated LLM_MODEL environment variable to: {llm_model}")

                if embedding_model:
                    app.config['SELECTED_EMBEDDING'] = embedding_model
                    os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                    # Clear the vector DB cache to force reinitialization with the new embedding model
                    from app.services.vector_db import _chroma_cache
                    _chroma_cache.clear()
                    logger.info(f"Updated TEXT_EMBEDDING_MODEL environment variable to: {embedding_model}")

                if vision_model:
                    app.config['SELECTED_VISION_MODEL'] = vision_model
                    os.environ['VISION_MODEL'] = vision_model  # Fix: Use VISION_MODEL instead of SELECTED_VISION_MODEL
                    logger.info(f"Updated VISION_MODEL environment variable to: {vision_model}")

                if use_vision is not None:
                    app.config['USE_VISION_MODEL'] = use_vision
                    os.environ['USE_VISION_MODEL'] = 'true' if use_vision else 'false'

                if use_vision_during_embedding is not None:
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_during_embedding else 'false'

                if filter_sensitivity:
                    os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

                if max_pdf_images is not None:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images)

                if show_filtered_images is not None:
                    os.environ['SHOW_FILTERED_IMAGES'] = 'true' if show_filtered_images else 'false'

                # Query Configuration
                if preamble is not None:
                    os.environ['QUERY_PREAMBLE'] = preamble

                if anti_hallucination_mode is not None:
                    os.environ['ANTI_HALLUCINATION_MODE'] = anti_hallucination_mode

                if anti_hallucination_custom_instructions is not None:
                    os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = anti_hallucination_custom_instructions

                if prompt_templates is not None:
                    os.environ['PROMPT_TEMPLATES'] = json.dumps(prompt_templates)

                if insufficient_info_phrases is not None:
                    os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(insufficient_info_phrases)

                if followup_question_templates is not None:
                    os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_question_templates)

                # Embedding Configuration
                if chunk_size is not None:
                    os.environ['EMBEDDING_CHUNK_SIZE'] = str(chunk_size)

                if chunk_overlap is not None:
                    os.environ['EMBEDDING_CHUNK_OVERLAP'] = str(chunk_overlap)

                if batch_size is not None:
                    os.environ['EMBEDDING_BATCH_SIZE'] = str(batch_size)

                if processing_threads is not None:
                    os.environ['EMBEDDING_PROCESSING_THREADS'] = str(processing_threads)

                # Create a success message based on which tab was saved
                if tab == 'models':
                    message = "Models settings saved successfully"
                elif tab == 'query':
                    message = "Query settings saved successfully"
                elif tab == 'embedding':
                    message = "Embedding settings saved successfully"
                else:
                    message = "All configuration saved successfully"

                return jsonify({"message": message}), 200
            else:
                return jsonify({"error": "Failed to save configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving unified configuration: {str(e)}")
            return jsonify({"error": f"Failed to save configuration: {str(e)}"}), 500

    # GET request - show the unified configuration page
    try:
        # Load models data
        models_data = get_models_data()

        # Load query configuration data
        query_data = get_query_config_data()

        # Load embedding configuration data
        embedding_data = get_embedding_config_data()

        # Combine all data
        combined_data = {**models_data, **query_data, **embedding_data}

        return render_template('unified_config.html', **combined_data)
    except Exception as e:
        logger.error(f"Error loading model settings page: {str(e)}")
        flash(f"Failed to load model settings: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/query_config_partial')
def query_config_partial():
    """Endpoint to get partial HTML for query configuration"""
    try:
        # Load query configuration data
        query_data = get_query_config_data()

        return render_template('query_config_partial.html', **query_data)
    except Exception as e:
        logger.error(f"Error loading query configuration partial: {str(e)}")
        return f"Error loading query configuration: {str(e)}", 500

@app.route('/admin/embedding_config_partial')
def embedding_config_partial():
    """Endpoint to get partial HTML for embedding configuration"""
    try:
        # Load embedding configuration data
        embedding_data = get_embedding_config_data()

        return render_template('embedding_config_partial.html', **embedding_data)
    except Exception as e:
        logger.error(f"Error loading embedding configuration partial: {str(e)}")
        return f"Error loading embedding configuration: {str(e)}", 500


@app.route('/admin/clean_urls', methods=['GET'])
@admin_required
def clean_urls():
    """Clean up malformed URLs in the database."""
    from app.utils.content_db import clean_malformed_urls

    results = clean_malformed_urls()

    if 'error' in results:
        flash(f"Error cleaning URLs: {results['error']}", "error")
    else:
        flash(f"URL cleanup complete: {results['fixed_count']} URLs fixed, {results['deleted_count']} URLs deleted, {results['total_processed']} total processed.", "success")

    return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/darkpan_demo', methods=['GET'])
@admin_required
def darkpan_demo():
    """Demo page for DarkPan Bootstrap 5 Admin Dashboard Template."""
    return render_template('darkpan_demo.html')

@app.route('/docs')
def documentation():
    """Simple documentation page."""
    return render_template('documentation.html')

@app.route('/admin/greeting_management', methods=['GET'])
@admin_required
@function_permission_required('greeting_management')
def greeting_management():
    """Greeting management interface."""
    try:
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates()

        # Group templates by type for easier display
        grouped_templates = {
            'welcome': [],
            'response': [],
            'return_user': [],
            'time_based': []
        }

        for template in templates:
            template_type = template.get('template_type', 'response')
            if template_type in grouped_templates:
                grouped_templates[template_type].append(template)

        return render_template('greeting_management.html',
                             templates=templates,
                             grouped_templates=grouped_templates)
    except Exception as e:
        logger.error(f"Error loading greeting management: {str(e)}")
        flash(f"Error loading greeting management: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))


        prompt_templates = query_params.get('prompt_templates', {
            'strict': '',
            'balanced': '',
            'off': '',
            'general': '',
            'document_specific': ''
        })
        insufficient_info_phrases = query_params.get('insufficient_info_phrases', [
            "I don't have enough information",
            "The provided context does not contain",
            "There is no information"
        ])
        followup_question_templates = query_params.get('followup_question_templates', {
            'default': '',
            'insufficient_info': ''
        })

        return render_template('query_config.html',
                              preamble=preamble,
                              anti_hallucination_modes=anti_hallucination_modes,
                              prompt_templates=prompt_templates,
                              insufficient_info_phrases=insufficient_info_phrases,
                              followup_question_templates=followup_question_templates)
    except Exception as e:
        logger.error(f"Error loading query configuration page: {str(e)}")
        flash(f"Failed to load query configuration: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/models/refresh', methods=['POST'])
@admin_required
@function_permission_required('model_settings')
def refresh_models():
    """API endpoint to refresh the models list from Ollama"""
    try:
        # Get fresh models data
        models_data = get_models_data()

        return jsonify({
            "success": True,
            "message": "Models refreshed successfully",
            "models": {
                "llm_count": len(models_data['models']),
                "embedding_count": len(models_data['embeddings']),
                "vision_count": len(models_data['vision_models'])
            }
        }), 200
    except Exception as e:
        logger.error(f"Error refreshing models: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to refresh models: {str(e)}"
        }), 500

@app.route('/api/available-models', methods=['GET'])
def get_available_models():
    """API endpoint to get available LLM models for chat interface"""
    try:
        # Get models data using the existing function
        models_data = get_models_data()

        # Extract LLM models with relevant information for chat interface
        llm_models = []
        for model in models_data['models']:
            model_info = {
                'name': model['name'],
                'display_name': model['name'].replace(':', ' ').replace('-', ' ').title(),
                'size': model.get('size', 0),
                'size_formatted': format_model_size(model.get('size', 0))
            }

            # Add model descriptions/capabilities based on model name
            if 'llama3.1' in model['name'].lower():
                if '8b' in model['name'].lower():
                    model_info['description'] = 'Fast, efficient model for general queries'
                    model_info['capability'] = 'General Purpose'
                elif '70b' in model['name'].lower():
                    model_info['description'] = 'Large, powerful model for complex analysis'
                    model_info['capability'] = 'Advanced Analysis'
                else:
                    model_info['description'] = 'Llama 3.1 language model'
                    model_info['capability'] = 'General Purpose'
            elif 'gemma' in model['name'].lower():
                if '4b' in model['name'].lower():
                    model_info['description'] = 'Compact multimodal model'
                    model_info['capability'] = 'Multimodal'
                elif '12b' in model['name'].lower():
                    model_info['description'] = 'Large multimodal model with vision capabilities'
                    model_info['capability'] = 'Advanced Multimodal'
                else:
                    model_info['description'] = 'Gemma language model'
                    model_info['capability'] = 'General Purpose'
            elif 'mistral' in model['name'].lower():
                model_info['description'] = 'Efficient model optimized for reasoning'
                model_info['capability'] = 'Reasoning'
            else:
                model_info['description'] = 'Language model for text generation'
                model_info['capability'] = 'General Purpose'

            llm_models.append(model_info)

        # Get current default model
        current_default = models_data.get('selected_model', models_data.get('default_llm', 'llama3.1:8b-instruct-q4_K_M'))

        return jsonify({
            "success": True,
            "models": llm_models,
            "default_model": current_default,
            "count": len(llm_models)
        }), 200

    except Exception as e:
        logger.error(f"Error getting available models for chat: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to get available models: {str(e)}",
            "models": [],
            "default_model": "llama3.1:8b-instruct-q4_K_M",
            "count": 0
        }), 500

# HTML Generator Routes
@app.route('/admin/html-generator', methods=['GET'])
@admin_required
@function_permission_required('html_generator')
def html_generator_page():
    """HTML Generator configuration page."""
    try:
        categories = utils.list_categories()
        return render_template('html_generator.html', categories=categories)
    except Exception as e:
        logger.error(f"Error loading HTML generator page: {str(e)}")
        flash(f"Error loading HTML generator: {str(e)}", "error")
        return redirect(url_for('admin.admin_dashboard'))

@app.route('/admin/html-generator/generate', methods=['POST'])
@admin_required
@function_permission_required('html_generator')
def generate_html_interface():
    """Generate a customized HTML chat interface."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No configuration data provided"}), 400

        # Generate the HTML interface
        success, message = html_generator.generate_html(data)

        if success:
            logger.info(f"HTML interface generated successfully: {data.get('filename', 'unknown')}")
            return jsonify({"success": True, "message": message}), 200
        else:
            logger.warning(f"HTML generation failed: {message}")
            return jsonify({"success": False, "message": message}), 400

    except Exception as e:
        logger.error(f"Error generating HTML interface: {str(e)}")
        return jsonify({"success": False, "message": f"Generation failed: {str(e)}"}), 500

@app.route('/admin/html-generator/files', methods=['GET'])
@admin_required
@function_permission_required('html_generator')
def list_generated_files():
    """List all generated HTML files."""
    try:
        files = html_generator.list_generated_files()
        return jsonify({"success": True, "files": files}), 200
    except Exception as e:
        logger.error(f"Error listing generated files: {str(e)}")
        return jsonify({"success": False, "message": f"Failed to list files: {str(e)}"}), 500

@app.route('/admin/html-generator/delete/<filename>', methods=['DELETE'])
@admin_required
@function_permission_required('html_generator')
def delete_generated_file(filename):
    """Delete a generated HTML file."""
    try:
        success, message = html_generator.delete_generated_file(filename)

        if success:
            logger.info(f"Generated HTML file deleted: {filename}")
            return jsonify({"success": True, "message": message}), 200
        else:
            logger.warning(f"Failed to delete generated file: {message}")
            return jsonify({"success": False, "message": message}), 400

    except Exception as e:
        logger.error(f"Error deleting generated file {filename}: {str(e)}")
        return jsonify({"success": False, "message": f"Failed to delete file: {str(e)}"}), 500

@app.route('/frontend/<filename>')
def serve_generated_file(filename):
    """Serve generated HTML files from the frontend directory."""
    try:
        from flask import send_from_directory
        return send_from_directory('frontend', filename)
    except Exception as e:
        logger.error(f"Error serving generated file {filename}: {str(e)}")
        return "File not found", 404

def format_model_size(size_bytes):
    """Format model size in human-readable format"""
    if size_bytes == 0:
        return "Unknown"

    # Convert bytes to appropriate unit
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"

@app.route('/admin/models/default', methods=['POST'])
def save_default_model_settings():
    """Endpoint to save default model settings"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        llm_model = data.get('llm_model')
        embedding_model = data.get('embedding_model')
        vision_model = data.get('vision_model')
        use_vision = data.get('use_vision')
        filter_pdf_images = data.get('filter_pdf_images')
        filter_sensitivity = data.get('filter_sensitivity')
        max_pdf_images = data.get('max_pdf_images')
        show_filtered_images = data.get('show_filtered_images')

        # Get vision during embedding setting
        use_vision_during_embedding = data.get('use_vision_during_embedding')

        # Get model parameters
        temperature = data.get('temperature')
        num_ctx = data.get('num_ctx')
        num_predict = data.get('num_predict')
        top_p = data.get('top_p')
        top_k = data.get('top_k')
        repeat_penalty = data.get('repeat_penalty')
        system_prompt = data.get('system_prompt')

        # Validate that at least one model is provided
        if not any([llm_model, embedding_model, vision_model]):
            return jsonify({"error": "No model settings provided"}), 400

        # Save the default models
        success = save_default_models(
            llm_model=llm_model,
            embedding_model=embedding_model,
            vision_model=vision_model,
            use_vision=use_vision,
            use_vision_during_embedding=use_vision_during_embedding,
            filter_pdf_images=filter_pdf_images,
            filter_sensitivity=filter_sensitivity,
            max_pdf_images=max_pdf_images,
            show_filtered_images=show_filtered_images,
            temperature=temperature,
            num_ctx=num_ctx,
            num_predict=num_predict,
            top_p=top_p,
            top_k=top_k,
            repeat_penalty=repeat_penalty,
            system_prompt=system_prompt
        )

        # Update the USE_VISION_MODEL environment variable if provided
        if use_vision is not None:
            use_vision_str = 'true' if use_vision else 'false'
            os.environ['USE_VISION_MODEL'] = use_vision_str
            app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
            logger.info(f"Updated vision model enabled setting: {app.config['USE_VISION_MODEL']}")

        # Update the USE_VISION_MODEL_DURING_EMBEDDING environment variable if provided
        if use_vision_during_embedding is not None:
            use_vision_during_embedding_str = 'true' if use_vision_during_embedding else 'false'
            os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
            app.config['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str.lower() == 'true'
            logger.info(f"Updated vision model during embedding setting: {app.config['USE_VISION_MODEL_DURING_EMBEDDING']}")

        # Update image filtering settings in environment variables
        if filter_pdf_images is not None:
            filter_pdf_images_str = 'true' if filter_pdf_images else 'false'
            os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
            app.config['FILTER_PDF_IMAGES'] = filter_pdf_images_str.lower() == 'true'
            logger.info(f"Updated PDF image filtering setting: {app.config['FILTER_PDF_IMAGES']}")

        if filter_sensitivity:
            if filter_sensitivity in ['low', 'medium', 'high']:
                os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                logger.info(f"Updated PDF image filter sensitivity: {filter_sensitivity}")

        if max_pdf_images is not None:
            try:
                max_images = int(max_pdf_images)
                if 1 <= max_images <= 50:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_images)
                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_images
                    logger.info(f"Updated maximum PDF images to analyze: {max_images}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid max_pdf_images value: {max_pdf_images}")

        if show_filtered_images is not None:
            show_filtered_images_str = 'true' if show_filtered_images else 'false'
            os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
            app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images_str.lower() == 'true'
            logger.info(f"Updated show filtered images setting: {app.config['SHOW_FILTERED_IMAGES']}")

        # Update model parameters in environment variables
        if temperature is not None:
            try:
                temp_value = float(temperature)
                if 0 <= temp_value <= 1:
                    os.environ['LLM_TEMPERATURE'] = str(temp_value)
                    app.config['LLM_TEMPERATURE'] = temp_value
                    logger.info(f"Updated LLM temperature: {temp_value}")
                else:
                    logger.warning(f"Invalid temperature value (must be between 0 and 1): {temp_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid temperature value: {temperature}")

        if num_ctx is not None:
            try:
                ctx_value = int(num_ctx)
                if ctx_value > 0:
                    os.environ['LLM_NUM_CTX'] = str(ctx_value)
                    app.config['LLM_NUM_CTX'] = ctx_value
                    logger.info(f"Updated LLM context window size: {ctx_value}")
                else:
                    logger.warning(f"Invalid context window value (must be positive): {ctx_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid context window value: {num_ctx}")

        if num_predict is not None:
            try:
                predict_value = int(num_predict)
                if predict_value > 0:
                    os.environ['LLM_NUM_PREDICT'] = str(predict_value)
                    app.config['LLM_NUM_PREDICT'] = predict_value
                    logger.info(f"Updated LLM max tokens to predict: {predict_value}")
                else:
                    logger.warning(f"Invalid max tokens value (must be positive): {predict_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid max tokens value: {num_predict}")

        if top_p is not None:
            try:
                top_p_value = float(top_p)
                if 0 <= top_p_value <= 1:
                    os.environ['LLM_TOP_P'] = str(top_p_value)
                    app.config['LLM_TOP_P'] = top_p_value
                    logger.info(f"Updated LLM top_p: {top_p_value}")
                else:
                    logger.warning(f"Invalid top_p value (must be between 0 and 1): {top_p_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid top_p value: {top_p}")

        if top_k is not None:
            try:
                top_k_value = int(top_k)
                if top_k_value > 0:
                    os.environ['LLM_TOP_K'] = str(top_k_value)
                    app.config['LLM_TOP_K'] = top_k_value
                    logger.info(f"Updated LLM top_k: {top_k_value}")
                else:
                    logger.warning(f"Invalid top_k value (must be positive): {top_k_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid top_k value: {top_k}")

        if repeat_penalty is not None:
            try:
                penalty_value = float(repeat_penalty)
                if penalty_value > 0:
                    os.environ['LLM_REPEAT_PENALTY'] = str(penalty_value)
                    app.config['LLM_REPEAT_PENALTY'] = penalty_value
                    logger.info(f"Updated LLM repeat penalty: {penalty_value}")
                else:
                    logger.warning(f"Invalid repeat penalty value (must be positive): {penalty_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid repeat penalty value: {repeat_penalty}")

        if system_prompt is not None:
            os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
            app.config['LLM_SYSTEM_PROMPT'] = system_prompt
            logger.info(f"Updated LLM system prompt: {system_prompt[:50]}...")

        if success:
            # Update environment variables with the new defaults immediately
            if llm_model:
                os.environ['LLM_MODEL'] = llm_model
                app.config['SELECTED_MODEL'] = llm_model
                logger.info(f"Updated LLM_MODEL environment variable to: {llm_model}")

            if embedding_model:
                os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                app.config['SELECTED_EMBEDDING'] = embedding_model
                # Clear the vector DB cache to force reinitialization with the new embedding model
                from app.services.vector_db import _chroma_cache
                _chroma_cache.clear()
                logger.info(f"Updated TEXT_EMBEDDING_MODEL environment variable to: {embedding_model}")

            if vision_model:
                os.environ['VISION_MODEL'] = vision_model
                app.config['SELECTED_VISION_MODEL'] = vision_model
                logger.info(f"Updated VISION_MODEL environment variable to: {vision_model}")

            # Also update the .env file to maintain consistency
            try:
                update_env_file(llm_model, embedding_model, vision_model)
            except Exception as env_error:
                logger.warning(f"Failed to update .env file: {str(env_error)}")

            return jsonify({"message": "Default models saved successfully"}), 200
        else:
            return jsonify({"error": "Failed to save default models"}), 500

    except Exception as e:
        logger.error(f"Error saving default models: {str(e)}")
        return jsonify({"error": f"Failed to save default models: {str(e)}"}), 500

@app.route('/admin/models', methods=['GET', 'POST'])
def manage_models():
    if request.method == 'GET':
        flash("AI Models configuration has been moved to the Model Settings page.", "info")
        return redirect(url_for('unified_config', _anchor='models'))

    # For POST requests, continue with the original function to maintain API compatibility
    if request.method == 'POST':
        try:
            data = request.get_json()
            if data:
                # Handle JSON request from the frontend
                selected_model = data.get('llm_model')
                selected_embedding = data.get('embedding_model')
                selected_vision = data.get('vision_model')
                use_vision = data.get('use_vision')
                use_vision_during_embedding = data.get('use_vision_during_embedding')
                filter_pdf_images = data.get('filter_pdf_images')
                filter_sensitivity = data.get('filter_sensitivity')
                max_pdf_images = data.get('max_pdf_images')

                # Get model parameters
                temperature = data.get('temperature')
                num_ctx = data.get('num_ctx')
                num_predict = data.get('num_predict')
                top_p = data.get('top_p')
                top_k = data.get('top_k')
                repeat_penalty = data.get('repeat_penalty')
                system_prompt = data.get('system_prompt')
            else:
                # Handle form submission (fallback)
                selected_model = request.form.get('llm_model')
                selected_embedding = request.form.get('embedding_model')
                selected_vision = request.form.get('vision_model')
                use_vision = request.form.get('use_vision')
                use_vision_during_embedding = request.form.get('use_vision_during_embedding')
                filter_pdf_images = request.form.get('filter_pdf_images')
                filter_sensitivity = request.form.get('filter_sensitivity')
                max_pdf_images = request.form.get('max_pdf_images')

                # Get model parameters
                temperature = request.form.get('temperature')
                if temperature:
                    temperature = float(temperature)
                num_ctx = request.form.get('num_ctx')
                if num_ctx:
                    num_ctx = int(num_ctx)
                num_predict = request.form.get('num_predict')
                if num_predict:
                    num_predict = int(num_predict)
                top_p = request.form.get('top_p')
                if top_p:
                    top_p = float(top_p)
                top_k = request.form.get('top_k')
                if top_k:
                    top_k = int(top_k)
                repeat_penalty = request.form.get('repeat_penalty')
                if repeat_penalty:
                    repeat_penalty = float(repeat_penalty)
                system_prompt = request.form.get('system_prompt')

            if selected_model:
                # Clear the cache to force reinitialization with the new model
                app.config['SELECTED_MODEL'] = selected_model
                os.environ['LLM_MODEL'] = selected_model
                logger.info(f"Updated LLM model to: {selected_model}")

            if selected_embedding:
                # Clear the vector DB cache to force reinitialization with the new embedding model
                from app.services.vector_db import _chroma_cache
                _chroma_cache.clear()

                app.config['SELECTED_EMBEDDING'] = selected_embedding
                os.environ['TEXT_EMBEDDING_MODEL'] = selected_embedding
                logger.info(f"Updated embedding model to: {selected_embedding} and cleared vector DB cache")

            if selected_vision:
                app.config['SELECTED_VISION_MODEL'] = selected_vision
                os.environ['VISION_MODEL'] = selected_vision
                logger.info(f"Updated vision model to: {selected_vision}")

            if use_vision is not None:
                # Convert to string 'true' or 'false' for environment variable
                use_vision_str = 'true' if use_vision in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
                os.environ['USE_VISION_MODEL'] = use_vision_str
                logger.info(f"Vision model enabled: {app.config['USE_VISION_MODEL']}")

            # Handle vision model during embedding setting
            if use_vision_during_embedding is not None:
                # Convert to string 'true' or 'false' for environment variable
                use_vision_during_embedding_str = 'true' if use_vision_during_embedding in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str.lower() == 'true'
                os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
                logger.info(f"Vision model during embedding enabled: {app.config['USE_VISION_MODEL_DURING_EMBEDDING']}")

            # Handle PDF image filtering configuration
            if filter_pdf_images is not None:
                # Convert to string 'true' or 'false' for environment variable
                filter_pdf_images_str = 'true' if filter_pdf_images in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['FILTER_PDF_IMAGES'] = filter_pdf_images_str.lower() == 'true'
                os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
                logger.info(f"PDF image filtering enabled: {app.config['FILTER_PDF_IMAGES']}")

            # Handle filter sensitivity
            if filter_sensitivity:
                # Validate sensitivity level
                if filter_sensitivity not in ['low', 'medium', 'high']:
                    filter_sensitivity = 'medium'  # Default to medium if invalid
                app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                logger.info(f"PDF image filter sensitivity set to: {filter_sensitivity}")

            # Handle show filtered images setting
            show_filtered_images = data.get('show_filtered_images') if data else request.form.get('show_filtered_images')
            if show_filtered_images is not None:
                # Convert to string 'true' or 'false' for environment variable
                show_filtered_images_str = 'true' if show_filtered_images in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images_str.lower() == 'true'
                os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
                logger.info(f"Show filtered images enabled: {app.config['SHOW_FILTERED_IMAGES']}")

            # Handle max PDF images to analyze
            if max_pdf_images:
                try:
                    # Convert to integer and validate
                    max_pdf_images_int = int(max_pdf_images)
                    if max_pdf_images_int < 1:
                        max_pdf_images_int = 10  # Default if too low
                    elif max_pdf_images_int > 50:
                        max_pdf_images_int = 50  # Cap at 50 to prevent excessive processing

                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_pdf_images_int
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images_int)
                    logger.info(f"Maximum PDF images to analyze set to: {max_pdf_images_int}")
                except (ValueError, TypeError):
                    # Default to 10 if not a valid number
                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = 10
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = '10'
                    logger.warning(f"Invalid max_pdf_images value: {max_pdf_images}, defaulting to 10")

            # Handle model parameters
            if temperature is not None:
                try:
                    temp_float = float(temperature)
                    if temp_float < 0:
                        temp_float = 0.0
                    elif temp_float > 1:
                        temp_float = 1.0
                    os.environ['LLM_TEMPERATURE'] = str(temp_float)
                    logger.info(f"Temperature set to: {temp_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid temperature value: {temperature}, using default")

            if num_ctx is not None:
                try:
                    ctx_int = int(num_ctx)
                    if ctx_int < 512:
                        ctx_int = 512
                    elif ctx_int > 32768:
                        ctx_int = 32768
                    os.environ['LLM_NUM_CTX'] = str(ctx_int)
                    logger.info(f"Context window size set to: {ctx_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid context window size value: {num_ctx}, using default")

            if num_predict is not None:
                try:
                    predict_int = int(num_predict)
                    if predict_int < 64:
                        predict_int = 64
                    elif predict_int > 4096:
                        predict_int = 4096
                    os.environ['LLM_NUM_PREDICT'] = str(predict_int)
                    logger.info(f"Max tokens to generate set to: {predict_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid max tokens value: {num_predict}, using default")

            if top_p is not None:
                try:
                    top_p_float = float(top_p)
                    if top_p_float < 0:
                        top_p_float = 0.0
                    elif top_p_float > 1:
                        top_p_float = 1.0
                    os.environ['LLM_TOP_P'] = str(top_p_float)
                    logger.info(f"Top P set to: {top_p_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid top_p value: {top_p}, using default")

            if top_k is not None:
                try:
                    top_k_int = int(top_k)
                    if top_k_int < 1:
                        top_k_int = 1
                    elif top_k_int > 100:
                        top_k_int = 100
                    os.environ['LLM_TOP_K'] = str(top_k_int)
                    logger.info(f"Top K set to: {top_k_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid top_k value: {top_k}, using default")

            if repeat_penalty is not None:
                try:
                    repeat_penalty_float = float(repeat_penalty)
                    if repeat_penalty_float < 1:
                        repeat_penalty_float = 1.0
                    elif repeat_penalty_float > 2:
                        repeat_penalty_float = 2.0
                    os.environ['LLM_REPEAT_PENALTY'] = str(repeat_penalty_float)
                    logger.info(f"Repeat penalty set to: {repeat_penalty_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid repeat_penalty value: {repeat_penalty}, using default")

            if system_prompt is not None:
                os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
                logger.info(f"System prompt updated")

            if data:  # If JSON request, return JSON response
                return jsonify({"message": "Models updated successfully"}), 200
            else:  # If form submission, redirect with flash message
                flash("Model and embedding selected successfully.", "success")
                return redirect(url_for('manage_models'))
        except Exception as e:
            logger.error(f"Error updating models: {str(e)}")
            if request.is_json:
                return jsonify({"error": f"Failed to update models: {str(e)}"}), 500
            flash(f"Failed to update models: {str(e)}", "error")
            return redirect(url_for('manage_models'))

    # GET request - show the models page
    try:
        # Use the improved model detection logic from get_models_data()
        models_data = get_models_data()
        models = models_data['models']
        embedding_models = models_data['embeddings']
        vision_models = models_data['vision_models']

        logger.info(f"Retrieved {len(models)} LLM models, {len(embedding_models)} embedding models, and {len(vision_models)} vision models from get_models_data()")

    except Exception as e:
        logger.error(f"Failed to get models data: {str(e)}")
        # Use fallback defaults if get_models_data() fails
        models = [
            {"name": "llama3.1:8b-instruct-q4_K_M", "size": 4.9 * 1024 * 1024 * 1024},
            {"name": "mistral:latest", "size": 4.1 * 1024 * 1024 * 1024},
            {"name": "llama3.2:3b-instruct-q4_K_M", "size": 2.0 * 1024 * 1024 * 1024},
            {"name": "gemma3:1b", "size": 815 * 1024 * 1024}
        ]
        embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]
        vision_models = [
            {"name": "llama3.2-vision:11b-instruct-q4_K_M", "size": 6.8 * 1024 * 1024 * 1024},
            {"name": "gemma3:4b-it-q4_K_M", "size": 3.3 * 1024 * 1024 * 1024},
            {"name": "gemma3:12b-it-q4_K_M", "size": 7.2 * 1024 * 1024 * 1024}
        ]
        flash(f"Failed to list models: {str(e)}", "error")

    # Ensure image filtering settings are always available in the template context
    # This prevents issues where the settings might disappear after page refresh
    filter_pdf_images = app.config.get('FILTER_PDF_IMAGES')
    if filter_pdf_images is None:
        # If not in app.config, try to get from environment variable
        filter_pdf_images = os.getenv('FILTER_PDF_IMAGES', 'true').lower() == 'true'
        # Store in app.config for future use
        app.config['FILTER_PDF_IMAGES'] = filter_pdf_images

    filter_sensitivity = app.config.get('PDF_IMAGE_FILTER_SENSITIVITY')
    if filter_sensitivity is None or filter_sensitivity not in ['low', 'medium', 'high']:
        # If not in app.config or invalid, try to get from environment variable
        filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')
        # Store in app.config for future use
        app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

    max_pdf_images = app.config.get('MAX_PDF_IMAGES_TO_ANALYZE')
    if max_pdf_images is None:
        # If not in app.config, try to get from environment variable
        try:
            max_pdf_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))
        except (ValueError, TypeError):
            max_pdf_images = 10
        # Store in app.config for future use
        app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_pdf_images

    show_filtered_images = app.config.get('SHOW_FILTERED_IMAGES')
    if show_filtered_images is None:
        # If not in app.config, try to get from environment variable
        show_filtered_images = os.getenv('SHOW_FILTERED_IMAGES', 'false').lower() == 'true'
        # Store in app.config for future use
        app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images

    logger.info(f"Rendering models.html with image filtering settings: filter_pdf_images={filter_pdf_images}, "
                f"filter_sensitivity={filter_sensitivity}, max_pdf_images={max_pdf_images}, "
                f"show_filtered_images={show_filtered_images}")

    # Get model parameters from environment variables
    temperature = float(os.getenv('LLM_TEMPERATURE', '0.7'))
    num_ctx = int(os.getenv('LLM_NUM_CTX', '4096'))
    num_predict = int(os.getenv('LLM_NUM_PREDICT', '256'))
    top_p = float(os.getenv('LLM_TOP_P', '0.9'))
    top_k = int(os.getenv('LLM_TOP_K', '40'))
    repeat_penalty = float(os.getenv('LLM_REPEAT_PENALTY', '1.1'))
    system_prompt = os.getenv('LLM_SYSTEM_PROMPT', 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.')

    return render_template('models.html',
                          models=models,
                          embeddings=embedding_models,
                          vision_models=vision_models,
                          selected_model=app.config.get('SELECTED_MODEL'),
                          selected_embedding=app.config.get('SELECTED_EMBEDDING'),
                          selected_vision=app.config.get('SELECTED_VISION_MODEL'),
                          use_vision=app.config.get('USE_VISION_MODEL', True),
                          use_vision_during_embedding=app.config.get('USE_VISION_MODEL_DURING_EMBEDDING', True),
                          filter_pdf_images=filter_pdf_images,
                          filter_sensitivity=filter_sensitivity,
                          max_pdf_images=max_pdf_images,
                          show_filtered_images=show_filtered_images,
                          default_llm=default_llm,
                          default_embedding=default_embedding,
                          default_vision=default_vision,
                          temperature=temperature,
                          num_ctx=num_ctx,
                          num_predict=num_predict,
                          top_p=top_p,
                          top_k=top_k,
                          repeat_penalty=repeat_penalty,
                          system_prompt=system_prompt)

@app.route('/query/<category>', methods=['POST'])
@csrf.exempt   # ← skip CSRF check entirely for this route
def query(category):
    try:
        # Validate category parameter
        if not category or not category.strip():
            return jsonify({"error": "Category parameter is required and cannot be empty."}), 400
        
        category = category.strip()
        logger.info(f"Processing query for category: '{category}'")
        
        # Check if category exists in available categories (warning only)
        from app.utils.helpers import list_categories
        available_categories = list_categories()
        if category not in available_categories:
            logger.warning(f"Category '{category}' not found in available categories: {available_categories}")
            # Don't block the query as the category might exist in the vector DB
        
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({"error": "Query is required."}), 400
        question = data['query']

        # Get optional selected model from request
        selected_model = data.get('selected_model')

        # Get anti-hallucination mode from request or use app config default
        anti_hallucination_mode = data.get('anti_hallucination_mode', app.config['ANTI_HALLUCINATION_MODE'])

        # Get client name if provided
        client_name = data.get('client_name')

        # Get device fingerprint if provided
        device_fingerprint = data.get('device_fingerprint')

        # Get session information
        session_id = data.get('session_id')
        session_start = data.get('session_start')

        # If no session_id is provided, generate a new one
        if not session_id:
            import uuid
            import datetime
            session_id = str(uuid.uuid4())
            session_start = datetime.datetime.now().isoformat()

        # Validate the mode
        if anti_hallucination_mode not in ['strict', 'balanced', 'off']:
            anti_hallucination_mode = 'strict'  # Default to strict if invalid

        logger.info(f"Query with anti-hallucination mode: {anti_hallucination_mode}, client: {client_name or 'Anonymous'}")

        # Pass the mode, client name, device fingerprint, session ID, and selected model to the query function
        result = query_category(category, question, anti_hallucination_mode, client_name, session_id, device_fingerprint, selected_model)

        # Ensure result is a dictionary
        if not isinstance(result, dict):
            logger.error(f"Query result is not a dictionary: {result}")
            result = {
                "answer": f"Error: {str(result)}",
                "sources": [],
                "images": [],
                "url_images": [],
                "pdf_images": [],
                "pdf_links": [],
                "metadata": {"error": str(result)},
                "analytics": {
                    "error": str(result),
                    "question_length": len(question),
                    "processing_time": 0,
                    "model_name": app.config['SELECTED_MODEL'],
                    "embedding_model": app.config['SELECTED_EMBEDDING']
                }
            }

        # Get the sources directly from the result, no need to re-process
        sources = result.get("sources", [])
        if not isinstance(sources, list) or not all(isinstance(s, dict) for s in sources):
            logger.warning(f"Invalid sources format from query service: {sources}")
            sources = []

        # Get images and links directly from the result
        images = result.get("images", [])
        url_images = result.get("url_images", [])  # New field for URL JPG images
        pdf_images = result.get("pdf_images", [])  # New field for PDF-extracted images
        pdf_links = result.get("pdf_links", [])
        answer = result.get("answer", "No answer available.")
        logger.info(f"Original answer before italicization: {answer}")
        # Italicize scientific names in the main answer
        answer = validate_and_italicize_scientific_names(answer)
        logger.info(f"Processed answer after italicization: {answer}")

        # Ensure metadata is a dictionary
        metadata = result.get("metadata", {})
        if not isinstance(metadata, dict):
            logger.warning(f"Metadata is not a dictionary, converting: {metadata}")
            metadata = {"error": str(metadata)} if metadata else {}

        # Get follow-up questions if available
        followup_questions = result.get("followup_questions", [])
        # Do NOT italicize scientific names in follow-up questions to avoid false positives
        # if followup_questions:
        #     followup_questions = [validate_and_italicize_scientific_names(q) for q in followup_questions]

        # Get analytics data if available
        analytics = result.get("analytics", {})

        # Get document thumbnails if available
        document_thumbnails = result.get("document_thumbnails", [])

        # Get model information for chat history
        current_model = app.config.get('SELECTED_MODEL', 'Unknown')
        current_embedding = app.config.get('SELECTED_EMBEDDING', 'Unknown')
        current_vision = app.config.get('SELECTED_VISION_MODEL', 'Unknown')

        # Save chat history with metadata, client name, device fingerprint, session information, follow-up questions, document thumbnails, and model info
        chat_id = db_utils.save_chat_history(category, question, answer, sources, images, pdf_links, metadata, url_images, pdf_images, client_name, session_id, session_start, device_fingerprint, document_thumbnails, anti_hallucination_mode, current_model, current_embedding, current_vision)

        # Save analytics data if available
        if analytics and chat_id:
            # Get geolocation data
            ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()

            db_utils.save_analytics(
                chat_id=chat_id,
                session_id=session_id,
                category=category,
                client_name=client_name,
                question_length=analytics.get("question_length", len(question)),
                answer_length=analytics.get("answer_length", len(answer)),
                processing_time=analytics.get("processing_time", 0),
                source_count=analytics.get("source_count", len(sources)),
                image_count=analytics.get("image_count", len(url_images) + len(pdf_images)),
                token_count=analytics.get("token_count"),
                model_name=analytics.get("model_name", app.config['SELECTED_MODEL']),
                embedding_model=analytics.get("embedding_model", app.config['SELECTED_EMBEDDING']),
                vision_model=analytics.get("vision_model", app.config.get('SELECTED_VISION_MODEL')),
                vision_enabled=analytics.get("vision_enabled", app.config.get('USE_VISION_MODEL', False)),
                images_filtered=analytics.get("images_filtered", 0),
                total_images_extracted=analytics.get("total_images_extracted", 0),
                filter_sensitivity=analytics.get("filter_sensitivity", app.config.get('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')),
                hallucination_detected=analytics.get("hallucination_detected", False),
                anti_hallucination_mode=anti_hallucination_mode,
                device_fingerprint=device_fingerprint,
                ip_address=ip_address,
                city=city,
                region=region,
                country=country,
                latitude=latitude,
                longitude=longitude
            )
            logger.info(f"Saved analytics for chat ID {chat_id}")

        # Log the query response for debugging
        logger.info(f"Query for category {category}: question={question}, answer={answer[:100]}..., sources={[s['display_name'] for s in sources]}")

        # Add hallucination warning to the UI if detected
        if metadata.get("hallucination_detected", False):
            logger.warning(f"Hallucination detected in response to: {question}")

        return jsonify({
            "answer": answer,
            "sources": sources,
            "images": images,
            "url_images": url_images,  # New field for URL JPG images
            "pdf_images": pdf_images,  # New field for PDF-extracted images
            "pdf_links": pdf_links,
            "followup_questions": followup_questions,  # Add follow-up questions
            "metadata": metadata,
            "session_id": session_id,  # Return session ID for client to store
            "session_start": session_start,  # Return session start time
            "document_thumbnails": document_thumbnails  # <-- Add this line
        })
    except Exception as e:
        logger.error(f"Query failed: {str(e)}")
        # Return a more detailed error response
        try:
            # Make sure session_id and session_start are defined
            if 'session_id' not in locals() or session_id is None:
                import uuid
                session_id = str(uuid.uuid4())

            if 'session_start' not in locals() or session_start is None:
                import datetime
                session_start = datetime.datetime.now().isoformat()

            return jsonify({
                "error": str(e),
                "answer": f"An error occurred while processing your query: {str(e)}",
                "sources": [],
                "images": [],
                "url_images": [],  # New field for URL JPG images
                "pdf_images": [],  # New field for PDF-extracted images
                "document_thumbnails": [],  # New field for document thumbnails
                "pdf_links": [],
                "followup_questions": [
                    "Can you try asking a different question?",
                    "Would you like to try a more specific query?",
                    "Can you rephrase your question?"
                ],
                "metadata": {"error": str(e)},
                "session_id": session_id,  # Return session ID for client to store
                "session_start": session_start  # Return session start time
            }), 500
        except Exception as nested_error:
            # If we can't even create the error response, return a simple error
            logger.error(f"Failed to create error response: {str(nested_error)}")
            return jsonify({"error": f"Critical error: {str(e)}"}), 500

@app.route('/admin/chat_history')
@admin_required
@function_permission_required('chat_history')
def chat_history():
    history = db_utils.get_chat_history()
    return render_template('chat_history.html', history=history)

@app.route('/admin/sessions')
@admin_required
@function_permission_required('chat_sessions')
def view_sessions():
    """View all chat sessions."""
    sessions = db_utils.get_sessions()
    return render_template('sessions.html', sessions=sessions)




@app.route('/admin/session/<session_id>')
@admin_required
def view_session(session_id):
    """View chat history for a specific session."""
    history = db_utils.get_chat_history(session_id)
    # Get the first entry to get session details
    session_info = None
    if history:
        session_info = {
            "session_id": session_id,
            "client_name": history[0].get("client_name", "Anonymous"),
            "device_fingerprint": history[0].get("device_fingerprint", "Unknown"),
            "start_time": history[0].get("session_start"),
            "end_time": history[0].get("session_end"),
            "message_count": len(history),
            "category": history[0].get("category")
        }
    return render_template('session_view.html', history=history, session=session_info)

@app.route('/admin/session/<session_id>/close', methods=['POST'])
@admin_required
def close_session(session_id):
    """Close a session by updating its end time."""
    success = db_utils.update_session_end(session_id)
    if success:
        flash(f"Session {session_id} closed successfully.", "success")
    else:
        flash(f"Failed to close session {session_id}.", "error")
    return redirect(url_for('view_sessions'))

@app.route('/clear_session', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during page unload
def clear_session():
    """Clear all session data when the app is closed."""
    try:
        # Get session ID from request - handle both JSON and FormData
        session_id = None

        # Try to get from JSON first
        if request.is_json:
            data = request.get_json() or {}
            session_id = data.get('session_id')
        else:
            # Try to get from form data (for sendBeacon)
            session_id = request.form.get('session_id')

        # If session ID is provided, close it on the server
        if session_id:
            db_utils.update_session_end(session_id)
            logger.info(f"Session {session_id} closed via clear_session endpoint")

        return jsonify({"success": True, "message": "Session data cleared"}), 200
    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """Get a fresh CSRF token."""
    try:
        from flask_wtf.csrf import generate_csrf
        token = generate_csrf()
        return jsonify({"csrf_token": token}), 200
    except Exception as e:
        logger.error(f"Error generating CSRF token: {str(e)}")
        return jsonify({"error": "Failed to generate CSRF token"}), 500

@app.route('/api/greeting', methods=['POST'])
def get_greeting():
    print("/api/greeting called. Request headers:", dict(request.headers))
    logger.info(f"/api/greeting called. Request headers: {dict(request.headers)}")
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Request data is required"}), 400

        client_name = data.get('client_name')
        if not client_name:
            return jsonify({"error": "Client name is required"}), 400

        context = data.get('context', {})

        # Initialize greeting manager
        greeting_manager = GreetingManager()

        # Get contextual greeting with enhanced context
        greeting_data = greeting_manager.get_contextual_greeting(client_name, context)

        # Log greeting usage for analytics if session_id is provided
        session_id = context.get('session_id')
        if session_id:
            greeting_manager.log_greeting_usage(session_id, client_name, greeting_data)

        return jsonify({
            "success": True,
            "greeting": greeting_data['greeting'],
            "greeting_data": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type'),
                "session_type": greeting_data.get('session_type'),
                "time_of_day": greeting_data.get('time_of_day'),
                "context": greeting_data.get('context', {})
            },
            "metadata": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type')
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting greeting: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "greeting": f"Hello {data.get('client_name', 'there')}!"  # Emergency fallback
        }), 500

@app.route('/api/greeting_templates', methods=['GET', 'POST'])
@admin_required
@function_permission_required('greeting_management')
def greeting_templates_api():
    """API endpoint for greeting templates CRUD operations."""
    greeting_manager = GreetingManager()

    if request.method == 'GET':
        try:
            template_type = request.args.get('type')
            templates = greeting_manager.get_greeting_templates(template_type)
            return jsonify({"success": True, "templates": templates}), 200
        except Exception as e:
            logger.error(f"Error getting greeting templates: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"success": False, "error": "Request data is required"}), 400

            template_type = data.get('template_type')
            greeting_text = data.get('greeting_text')
            context_conditions = data.get('context_conditions', {})
            weight = data.get('weight', 1)

            if not template_type or not greeting_text:
                return jsonify({"success": False, "error": "Template type and greeting text are required"}), 400

            success = greeting_manager.add_greeting_template(
                template_type, greeting_text, context_conditions, weight
            )

            if success:
                return jsonify({"success": True, "message": "Greeting template added successfully"}), 201
            else:
                return jsonify({"success": False, "error": "Failed to add greeting template"}), 500

        except Exception as e:
            logger.error(f"Error adding greeting template: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/greeting_templates/<int:template_id>', methods=['PUT', 'DELETE'])
@admin_required
@function_permission_required('greeting_management')
def greeting_template_api(template_id):
    """API endpoint for managing individual greeting templates."""
    if request.method == 'PUT':
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        # Accept all relevant fields
        allowed_fields = ['template_type', 'greeting_text', 'weight', 'is_active', 'context_conditions']
        update_fields = {k: v for k, v in data.items() if k in allowed_fields}
        if not update_fields:
            return jsonify({'success': False, 'error': 'No valid fields provided'}), 400
        try:
            success = greeting_manager.update_greeting_template(template_id, **update_fields)
            if success:
                return jsonify({'success': True, 'message': 'Template updated successfully'}), 200
            else:
                return jsonify({'success': False, 'error': 'Template not found'}), 404
        except Exception as e:
            logger.error(f"Error updating template: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    elif request.method == 'DELETE':
        try:
            success = greeting_manager.delete_greeting_template(template_id)
            if success:
                return jsonify({'success': True, 'message': 'Template deleted successfully'}), 200
            else:
                return jsonify({'success': False, 'error': 'Template not found'}), 404
        except Exception as e:
            logger.error(f"Error deleting template: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/vision_embedding', methods=['GET'])
def get_vision_embedding_setting():
    """Get the global vision model during embedding setting."""
    try:
        # Get the current setting from environment variable
        use_vision_during_embedding = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

        # Return the setting as JSON
        return jsonify({
            "enabled": use_vision_during_embedding,
            "message": "Vision model during embedding is " + ("enabled" if use_vision_during_embedding else "disabled")
        }), 200
    except Exception as e:
        logger.error(f"Error getting vision embedding setting: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/model-performance-analysis', methods=['GET'])
@admin_required
@function_permission_required('model_performance_analysis')
def get_model_performance_analysis():
    """Get model performance analysis for development research."""
    try:
        # Get date range filters from request
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Get the analysis data
        analysis = db_utils.get_model_performance_analysis(start_date=start_date, end_date=end_date)

        return jsonify({
            "success": True,
            "analysis": analysis,
            "message": f"Model performance analysis generated with {len(analysis.get('model_comparisons', []))} configurations"
        }), 200

    except Exception as e:
        logger.error(f"Error getting model performance analysis: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "analysis": {
                "model_comparisons": [],
                "anti_hallucination_effectiveness": [],
                "category_performance": {},
                "summary": {"error": str(e)}
            }
        }), 500

@app.route('/api/cleanup-malformed-urls', methods=['POST'])
@admin_required
def cleanup_malformed_urls_api():
    """API endpoint to cleanup malformed URLs from the database."""
    try:
        # Get the cleanup results
        results = db_utils.cleanup_malformed_urls()
        
        return jsonify({
            "success": True,
            "message": f"Cleanup completed. {results.get('cleaned_count', 0)} malformed URLs removed.",
            "results": results
        }), 200

    except Exception as e:
        logger.error(f"Error cleaning up malformed URLs: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/check_duplicate', methods=['POST'])
@csrf.exempt
@function_permission_required('upload_files')
def api_check_duplicate():
    """API endpoint to check for duplicate files."""
    try:
        # Get file and category from form data or JSON
        if request.content_type == 'application/json':
            data = request.get_json()
            filename = data.get('filename')
            category = data.get('category')
            file_obj = None
            if not filename:
                return jsonify({'error': 'Filename is required.'}), 400
        else:  # Handle multipart/form-data
            file_obj = request.files.get('file')
            filename = None
            if file_obj and file_obj.filename:
                filename = secure_filename(file_obj.filename)
                # Reset file pointer to beginning in case it was read already
                file_obj.seek(0)
            category = request.form.get('category')

        if not filename:
            return jsonify({'error': 'No file provided or invalid filename'}), 400
        if not category:
            return jsonify({'error': 'Category is required.'}), 400

        # If we have a file object, use it, otherwise just check by filename
        if file_obj:
            is_duplicate, duplicate_info = check_duplicate_pdf(file_obj, category)
        else:
            # If no file object, just check if a file with this name exists in the category
            from app.utils.helpers import TEMP_FOLDER
            file_path = os.path.join(TEMP_FOLDER, category, filename)
            is_duplicate = os.path.exists(file_path)
            duplicate_info = None

        response_data = {'is_duplicate': is_duplicate}
        
        if is_duplicate and duplicate_info:
            response_data['duplicate_info'] = duplicate_info
            if duplicate_info.get('type') == 'filename_match':
                response_data['message'] = f"A file with the same name already exists in category '{category}'"
            elif duplicate_info.get('type') == 'content_match':
                response_data['message'] = f"A file with identical content already exists in category '{category}'"
            else:
                response_data['message'] = f"A duplicate file was found in category '{category}'"
        elif is_duplicate:
            response_data['message'] = f"A file with the same name already exists in category '{category}'"

        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in check_duplicate: {str(e)}", exc_info=True)
        return jsonify({'error': f'Error checking for duplicate: {str(e)}'}), 500

@limiter.exempt
def serve_file(category, filename):
    """Serve PDF files directly from the app."""
    try:
        logger.info(f"[serve_file] category={category}, filename={filename}")
        
        # Ensure TEMP_FOLDER is an absolute path
        temp_folder_abs = os.path.abspath(TEMP_FOLDER)
        logger.info(f"[serve_file] TEMP_FOLDER (absolute): {temp_folder_abs}")
        
        # Try multiple path structures in order of preference
        possible_paths = []
        
        # 1. New structure: category/pdf_base_name/filename
        pdf_base_name = os.path.splitext(filename)[0]
        new_path = os.path.join(temp_folder_abs, category, pdf_base_name, filename)
        possible_paths.append(("new structure", new_path))
        
        # 2. Old structure: category/filename (direct)
        old_path = os.path.join(temp_folder_abs, category, filename)
        possible_paths.append(("old structure", old_path))
        
        # 3. Legacy _temp structure: _temp/category/filename
        legacy_path = os.path.join(temp_folder_abs, "_temp", category, filename)
        possible_paths.append(("legacy _temp", legacy_path))
        
        # Check each possible path
        for path_name, file_path in possible_paths:
            logger.info(f"[serve_file] Checking {path_name}: {file_path} (exists: {os.path.exists(file_path)})")
            if os.path.exists(file_path) and os.path.isfile(file_path):
                logger.info(f"[serve_file] Serving file from {path_name}: {file_path}")
                # Set the appropriate content type
                if filename.lower().endswith('.pdf'):
                    return send_file(file_path, mimetype='application/pdf')
                else:
                    return send_file(file_path)
        
        # If no file found, log all checked paths
        logger.warning(f"[serve_file] File not found: {filename} (category: {category})")
        logger.warning(f"[serve_file] Checked paths: {[p[1] for p in possible_paths]}")
        flash(f"File not found: {filename}", "error")
        return redirect(url_for('list_files'))
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        flash(f"Error serving file: {str(e)}", "error")
        return redirect(url_for('list_files'))

@app.route('/<category>/<pdf_name>/pdf_images/<filename>')
@limiter.exempt
def serve_pdf_image_new(category, pdf_name, filename):
    """
    Serve a PDF image from the new directory structure:
    /data/temp/{category}/{pdf_name}/pdf_images/{filename}
    """
    try:
        # Construct the full path to the image file from the project root
        image_path = os.path.join(PROJECT_ROOT, 'data', 'temp', category, pdf_name, 'pdf_images', filename)
        
        # Log the path for debugging
        logger.info(f"Serving image from path: {image_path}")

        if os.path.exists(image_path) and os.path.isfile(image_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            return send_file(image_path, mimetype=content_type)
        else:
            logger.error(f"Image file not found: {image_path}")
            return "Image not found", 404
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        return f"Error serving image: {str(e)}", 500

@app.route('/<category>/<pdf_name>/pdf_images/cover_image/<filename>')
@limiter.exempt
def serve_pdf_cover_image(category, pdf_name, filename):
    """
    Serve a PDF cover image from the new directory structure:
    /data/temp/{category}/{pdf_name}/pdf_images/cover_image/{filename}
    """
    try:
        # Construct the full path to the cover image from the project root
        image_path = os.path.join(PROJECT_ROOT, 'data', 'temp', category, pdf_name, 'pdf_images', 'cover_image', filename)

        # Log the path for debugging
        logger.info(f"Serving cover image from path: {image_path}")

        if os.path.exists(image_path) and os.path.isfile(image_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            logger.info(f"Serving cover image: {image_path}")
            return send_file(image_path, mimetype=content_type)
        else:
            logger.warning(f"Cover image file not found: {image_path}")

            # Try to find the PDF file and extract the first page on-the-fly
            pdf_file_path = os.path.join(TEMP_FOLDER, category, pdf_name, f"{pdf_name}.pdf")
            logger.debug(f"Looking for PDF file at: {pdf_file_path}")

            if os.path.exists(pdf_file_path) and os.path.isfile(pdf_file_path):
                try:
                    # Create the cover_image directory if it doesn't exist
                    cover_image_dir = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images", "cover_image")
                    os.makedirs(cover_image_dir, exist_ok=True)

                    # Import fitz here to avoid circular imports
                    import fitz

                    # Open the PDF and extract the first page
                    doc = fitz.open(pdf_file_path)
                    if doc.page_count > 0:
                        page = doc[0]
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                        pix.save(image_path)

                        logger.info(f"Generated cover image on-the-fly: {image_path}")
                        return send_file(image_path, mimetype='image/jpeg')
                    else:
                        logger.warning(f"PDF has no pages: {pdf_file_path}")
                except Exception as extract_error:
                    logger.error(f"Failed to extract cover image on-the-fly: {str(extract_error)}")
            else:
                logger.warning(f"PDF file not found: {pdf_file_path}")

            # If we still can't serve the image, check for any image in the pdf_images directory
            try:
                # Look for any image in the pdf_images directory
                pdf_images_dir = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images")
                logger.debug(f"Looking for any image in: {pdf_images_dir}")

                if os.path.exists(pdf_images_dir) and os.path.isdir(pdf_images_dir):
                    # Get the first image file in the directory
                    image_files = [f for f in os.listdir(pdf_images_dir)
                                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))
                                  and os.path.isfile(os.path.join(pdf_images_dir, f))]

                    if image_files:
                        # Use the first image as a fallback
                        fallback_image = os.path.join(pdf_images_dir, image_files[0])
                        logger.info(f"Using fallback image from pdf_images directory: {fallback_image}")
                        return send_file(fallback_image, mimetype='image/jpeg')
                    else:
                        logger.warning(f"No image files found in {pdf_images_dir}")
                else:
                    logger.warning(f"PDF images directory not found: {pdf_images_dir}")

                # Check for a category-level placeholder in the _temp directory
                category_placeholder = os.path.join(TEMP_FOLDER, category, "placeholder.jpg")
                logger.debug(f"Looking for category placeholder at: {category_placeholder}")

                if os.path.exists(category_placeholder) and os.path.isfile(category_placeholder):
                    logger.info(f"Using category placeholder: {category_placeholder}")
                    return send_file(category_placeholder, mimetype='image/jpeg')

                # If no placeholder is found, create a simple text-based placeholder
                from PIL import Image, ImageDraw, ImageFont

                # Create a blank image
                img = Image.new('RGB', (400, 300), color=(240, 240, 240))
                d = ImageDraw.Draw(img)

                # Try to use a system font
                try:
                    font = ImageFont.truetype("arial.ttf", 20)
                except IOError:
                    try:
                        # Try another common font on different systems
                        font = ImageFont.truetype("DejaVuSans.ttf", 20)
                    except IOError:
                        font = ImageFont.load_default()

                # Add text to the image
                text = f"No thumbnail for {pdf_name}"
                text_width = d.textlength(text, font=font)
                d.text(((400-text_width)/2, 140), text, fill=(0, 0, 0), font=font)

                # Save the image to the cover_image directory
                os.makedirs(os.path.dirname(image_path), exist_ok=True)
                img.save(image_path)

                logger.info(f"Created dynamic placeholder image: {image_path}")
                return send_file(image_path, mimetype='image/jpeg')

            except Exception as placeholder_error:
                logger.error(f"Error creating placeholder image: {str(placeholder_error)}")

            # If all else fails, return a 404
            return "Cover image not found", 404
    except Exception as e:
        logger.error(f"Error serving cover image {filename}: {str(e)}")
        return f"Error serving cover image: {str(e)}", 500

@app.route('/<category>/<pdf_name>/pdf_tables/<filename>')
@limiter.exempt
def serve_pdf_table_new(category, pdf_name, filename):
    """
    Serve a PDF table from the new directory structure:
    /data/temp/{category}/{pdf_name}/pdf_tables/{filename}
    """
    try:
        # Construct the full path to the table file from the project root
        table_path = os.path.join(PROJECT_ROOT, 'data', 'temp', category, pdf_name, 'pdf_tables', filename)
        
        # Log the path for debugging
        logger.info(f"Serving table from path: {table_path}")

        if os.path.exists(table_path) and os.path.isfile(table_path):
            # For HTML tables, set the content type to text/html
            if filename.lower().endswith('.html'):
                return send_file(table_path, mimetype='text/html')
            else:
                return send_file(table_path)
        else:
            logger.error(f"Table file not found: {table_path}")
            return "Table not found", 404
    except Exception as e:
        logger.error(f"Error serving table {filename}: {str(e)}")
        return f"Error serving table: {str(e)}", 500

# Keep the old routes for backward compatibility
@app.route('/pdf_images/<category>/<filename>')
@limiter.exempt
def serve_pdf_image(category, filename):
    """
    Serve a PDF image from the temporary directory.
    NOTE: This is a legacy route, prefer the new structure.
    """
    try:
        # Construct the full path to the image file from the project root
        image_path = os.path.join(PROJECT_ROOT, 'data', 'temp', category, 'pdf_images', filename)

        # Log the path for debugging
        logger.info(f"Serving legacy image from path: {image_path}")
        
        # Check if file exists
        if os.path.exists(image_path) and os.path.isfile(image_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            return send_file(image_path, mimetype=content_type)
        else:
            logger.error(f"Image file not found: {image_path}")
            return "Image not found", 404
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        return f"Error serving image: {str(e)}", 500

@app.route('/pdf_tables/<category>/<filename>')
@limiter.exempt
def serve_pdf_table(category, filename):
    """
    Serve a PDF table from the temporary directory.
    NOTE: This is a legacy route, prefer the new structure.
    """
    try:
        # Construct the full path to the table file from the project root
        table_path = os.path.join(PROJECT_ROOT, 'data', 'temp', category, 'pdf_tables', filename)
        
        # Log the path for debugging
        logger.info(f"Serving legacy table from path: {table_path}")

        # Check if file exists
        if not os.path.exists(table_path):
            logger.error(f"Table file not found: {table_path}")
            return "Table not found", 404

        # For HTML tables, set the content type to text/html
        if filename.lower().endswith('.html'):
            return send_file(table_path, mimetype='text/html')
        else:
            return send_file(table_path)
    except Exception as e:
        logger.error(f"Error serving table {filename}: {str(e)}")
        return f"Error serving table: {str(e)}", 500

# Middleware to track user locations
@app.before_request
def track_user_location():
    print("before_request:", request.path)
    logger.info(f"before_request: {request.path}")
    """Track user location for analytics."""
    # Skip for static files and API endpoints
    if request.path.startswith('/static/') or request.path.startswith('/api/'):
        return

    try:
        # Get device fingerprint from request
        device_fingerprint = request.cookies.get('device_fingerprint')
        if not device_fingerprint:
            return

        # Get client name from session or cookie
        client_name = request.cookies.get('client_name')

        # Get session ID
        session_id = request.cookies.get('session_id')

        # Get geolocation data
        ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()

        # Save geolocation data
        geoip_analytics.save_geoip_data(
            ip_address=ip_address,
            device_fingerprint=device_fingerprint,
            client_name=client_name,
            city=city,
            region=region,
            country=country,
            latitude=latitude,
            longitude=longitude,
            user_agent=request.user_agent.string,
            page_url=request.path,
            session_id=session_id
        )
    except Exception as e:
        logger.error(f"Error tracking user location: {str(e)}")

@app.route('/admin/forms', methods=['GET'])
@admin_required
def list_forms():
    forms = forms_db.get_all_forms()
    return render_template('admin_forms.html', forms=forms, page_title="Manage Forms")

@app.route('/admin/forms/new', methods=['GET', 'POST'])
@admin_required
def create_form():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        try:
            fields = json.loads(request.form.get('fields'))
        except json.JSONDecodeError:
            flash('Invalid JSON format for fields.', 'error')
            return render_template('admin_edit_form.html', form=None, page_title="Create New Form")
            
        is_active = 'is_active' in request.form
        
        forms_db.create_form(name, description, fields, is_active)
        flash('Form created successfully!', 'success')
        return redirect(url_for('list_forms'))
        
    return render_template('admin_edit_form.html', form=None, page_title="Create New Form")

@app.route('/admin/forms/<int:form_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_form(form_id):
    form = forms_db.get_form(form_id)
    if not form:
        flash('Form not found.', 'error')
        return redirect(url_for('list_forms'))

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        try:
            fields = json.loads(request.form.get('fields'))
        except json.JSONDecodeError:
            flash('Invalid JSON format for fields.', 'error')
            return render_template('admin_edit_form.html', form=form, page_title="Edit Form")

        is_active = 'is_active' in request.form
        
        forms_db.update_form(form_id, name, description, fields, is_active)
        flash('Form updated successfully!', 'success')
        return redirect(url_for('list_forms'))
        
    return render_template('admin_edit_form.html', form=form, page_title="Edit Form")

@app.route('/admin/forms/<int:form_id>/delete', methods=['POST'])
@admin_required
def delete_form(form_id):
    forms_db.delete_form(form_id)
    flash('Form deleted successfully!', 'success')
    return redirect(url_for('list_forms'))

@app.route('/admin/forms/<int:form_id>/view', methods=['GET'])
@admin_required
def view_form(form_id):
    form = forms_db.get_form(form_id)
    if not form:
        flash('Form not found.', 'error')
        return redirect(url_for('list_forms'))
    
    # Get PDFs associated with this form
    associated_pdfs = forms_db.get_pdfs_by_form_id(form_id)
    
    # Get form usage statistics
    usage_stats = forms_db.get_form_usage_statistics(form_id)
    
    return render_template('admin_form_preview.html', 
                         form=form, 
                         associated_pdfs=associated_pdfs,
                         usage_stats=usage_stats,
                         page_title="View Form")

@app.route('/admin/submissions', methods=['GET'])
@admin_required
def list_submissions():
    submissions = forms_db.get_form_submissions()
    return render_template('admin_submissions.html', submissions=submissions, page_title="Form Submissions")

@app.route('/admin/submissions/<int:submission_id>/delete', methods=['POST'])
@admin_required
def delete_submission(submission_id):
    forms_db.delete_form_submission(submission_id)
    flash('Submission deleted successfully!', 'success')
    return redirect(url_for('list_submissions'))

@app.route('/download_gated/<path:filename>', methods=['GET', 'POST'])
def download_gated_pdf(filename):
    try:
        logger.info(f"[download_gated_pdf] Attempting to find PDF: {filename}")
        pdf_document = get_pdf_by_filename_only(filename)
        if not pdf_document:
            # Try stripping a leading timestamp (e.g., 20250706114854_)
            import re
            m = re.match(r'^(\d{14}_)?(.+)$', filename)
            if m:
                alt_filename = m.group(2)
                logger.info(f"[download_gated_pdf] Trying alternate filename: {alt_filename}")
                pdf_document = get_pdf_by_filename_only(alt_filename)
        if not pdf_document:
            # Try matching by original filename (case-insensitive)
            logger.info(f"[download_gated_pdf] Trying match by original filename: {filename}")
            all_pdfs = forms_db.get_all_pdfs() if hasattr(forms_db, 'get_all_pdfs') else []
            for doc in all_pdfs:
                if doc.get('original_filename', '').lower() == filename.lower():
                    pdf_document = doc
                    logger.info(f"[download_gated_pdf] Found by original filename: {doc.get('original_filename')}")
                    break
        
        # Additional fallback: try to match non-OCR filename to base filename in database
        if not pdf_document and filename.startswith('non_ocr_'):
            base_filename = filename[8:]  # Remove 'non_ocr_' prefix
            logger.info(f"[download_gated_pdf] Trying base filename without non_ocr_ prefix: {base_filename}")
            pdf_document = get_pdf_by_filename_only(base_filename)
            if pdf_document:
                logger.info(f"[download_gated_pdf] Found PDF by base filename: {base_filename}")
                # Update the document to reflect that we're serving the non-OCR version
                pdf_document['requested_filename'] = filename
        
        # If still not found, create a fallback document from vector database
        if not pdf_document:
            logger.info(f"[download_gated_pdf] No database record found, creating fallback document")
            
            # Try to normalize the filename to match actual files on disk
            normalized_filenames = [filename]
            
            # Handle common malformed patterns
            import re
            
            # Pattern: 20250706141034RISEv31n1.pdf -> 20250706141034_RISE_v31n1.pdf
            if re.match(r'(\d{14})([A-Z]+)', filename):
                # Insert underscore before category name
                match = re.match(r'(\d{14})([A-Z]+)(.+)', filename)
                if match:
                    timestamp, category_name, rest = match.groups()
                    normalized_name = f"{timestamp}_{category_name}{rest}"
                    normalized_filenames.append(normalized_name)
                    logger.info(f"[download_gated_pdf] Trying normalized filename: {normalized_name}")
            
            # Pattern: missing underscore before version
            for name in normalized_filenames[:]:  # Copy list to avoid modifying during iteration
                if 'v' in name and '_v' not in name:
                    # Insert underscore before version
                    normalized_with_v = re.sub(r'([a-zA-Z])v(\d)', r'\1_v\2', name)
                    if normalized_with_v != name:
                        normalized_filenames.append(normalized_with_v)
                        logger.info(f"[download_gated_pdf] Trying with version underscore: {normalized_with_v}")
            
            # Check if any of the normalized filenames exist on disk
            found_file = None
            found_category = None
            
            # Get all available categories dynamically
            available_categories = list_categories()
            for category in available_categories:
                for test_filename in normalized_filenames:
                    # Check multiple possible file structures
                    possible_paths = []
                    
                    # 1. Flat structure: category/filename
                    flat_path = os.path.join(TEMP_FOLDER, category, test_filename)
                    possible_paths.append(("flat", flat_path))
                    
                    # 2. Hierarchical structure: category/pdf_base_name/filename
                    # Extract PDF base name from filename for hierarchical structure
                    pdf_base_name = os.path.splitext(test_filename)[0]
                    # For non-OCR files, use the base name without non_ocr_ prefix
                    if test_filename.startswith('non_ocr_'):
                        pdf_base_name = test_filename[8:]  # Remove 'non_ocr_' prefix
                        pdf_base_name = os.path.splitext(pdf_base_name)[0]
                    hierarchical_path = os.path.join(TEMP_FOLDER, category, pdf_base_name, test_filename)
                    possible_paths.append(("hierarchical", hierarchical_path))
                    
                    # Check each possible path
                    for path_type, file_path in possible_paths:
                        logger.info(f"[download_gated_pdf] Checking {path_type}: {file_path}")
                        if os.path.exists(file_path):
                            found_file = test_filename
                            found_category = category
                            logger.info(f"[download_gated_pdf] Found file on disk ({path_type}): {file_path}")
                            break
                    
                    if found_file:
                        break
                if found_file:
                    break
            
            if found_file and found_category:
                # Create a minimal document for gated download
                pdf_document = {
                    'id': None,
                    'filename': found_file,
                    'original_filename': found_file,
                    'category': found_category,
                    'form_id': 1,  # Default form ID
                    'source_url_id': None
                }
                logger.info(f"[download_gated_pdf] Created fallback document for: {found_file}")
            
            if not pdf_document:
                logger.error(f"[download_gated_pdf] Document not found for: {filename}")
                flash('Document not found.', 'error')
                return '''
                <script>
                    alert('Document not found.');
                    window.close();
                </script>
                '''
        
        # Check if this is a gated PDF (has form_id) - if so, require form submission
        # Only serve directly if it's explicitly NOT a gated PDF (form_id is None/False)
        if pdf_document.get('form_id') is None:
            logger.info(f"[download_gated_pdf] No form required for {filename}, serving directly")
            return serve_file(pdf_document['category'], filename)
        
        # Try to get form from database
        form = None
        if hasattr(forms_db, 'get_pdf_document_form') and pdf_document.get('id'):
            try:
                form = forms_db.get_pdf_document_form(pdf_document['id'])
            except Exception as e:
                logger.warning(f"Could not get form from database: {e}")
        
        # If no form found but we have a form_id, try to get the form directly
        if not form and pdf_document.get('form_id'):
            try:
                form = forms_db.get_form(pdf_document['form_id'])
                logger.info(f"Retrieved form directly by form_id {pdf_document['form_id']}: {form['name'] if form else 'None'}")
            except Exception as e:
                logger.warning(f"Could not get form by form_id: {e}")
        
        # If no form found, create a default form for gated PDFs
        if not form:
            logger.info(f"Creating default form for gated PDF: {filename}")
            form = {
                'id': 1,
                'name': 'Default PDF Access Form',
                'is_active': True,
                'fields': [
                    {
                        'name': 'name',
                        'label': 'Name',
                        'type': 'text',
                        'required': True,
                        'placeholder': 'Enter your full name'
                    },
                    {
                        'name': 'email',
                        'label': 'Email',
                        'type': 'email',
                        'required': True,
                        'placeholder': 'Enter your email address'
                    },
                    {
                        'name': 'organization',
                        'label': 'Organization',
                        'type': 'text',
                        'required': False,
                        'placeholder': 'Enter your organization (optional)'
                    }
                ]
            }
        
        if not form.get('is_active', True):
            return serve_file(pdf_document['category'], filename)
        # Use session to track form submission
        # Use filename as fallback if no ID available
        pdf_id = pdf_document.get("id") or filename.replace('/', '_').replace('\\', '_')
        session_key = f'gated_pdf_{pdf_id}_submitted'
        if session.get(session_key):
            return handle_gated_download(pdf_document)
        if request.method == 'POST':
            logger.info(f"[download_gated_pdf] POST request received for: {filename}")
            logger.info(f"[download_gated_pdf] Form data: {dict(request.form)}")
            
            submission_data = {}
            for field in form['fields']:
                field_name = field['name']
                field_value = request.form.get(field_name, '')
                submission_data[field_name] = field_value
                logger.info(f"[download_gated_pdf] Field {field_name}: {field_value}")
            # Try to create form submission if forms_db is available
            submission_id = None
            if hasattr(forms_db, 'create_form_submission') and pdf_document.get('id'):
                try:
                    submission_id = forms_db.create_form_submission(
                        form_id=form['id'],
                        pdf_document_id=pdf_document['id'],
                        submission_data=submission_data,
                        ip_address=request.remote_addr,
                        user_agent=request.user_agent.string
                    )
                except Exception as e:
                    logger.warning(f"Could not create form submission in database: {e}")
            
            # Mark as submitted in session regardless of database submission
            session[session_key] = True
            flash('Thank you for your submission. You can now download the file.', 'success')
            logger.info(f"[download_gated_pdf] Serving file - category: {pdf_document['category']}, filename: {filename}")
            return handle_gated_download(pdf_document)
        return render_template('gated_download_form.html', 
                             form=form, 
                             filename=pdf_document['original_filename'], 
                             page_title="Download Authorization")
    except Exception as e:
        logger.error(f"Error in download_gated_pdf: {str(e)}")
        logger.error(f"[download_gated_pdf] Exception traceback:", exc_info=True)
        flash('An error occurred while processing your request.', 'error')
        return '''
        <script>
            alert('An error occurred while processing your request.');
            window.close();
        </script>
        '''

def handle_gated_download(pdf_document):
    try:
        logger.info(f"[handle_gated_download] Called with document: {pdf_document}")
        
        # If we have source URL, redirect to it
        if pdf_document.get('source_url_id'):
            source_url = get_source_url_by_id(pdf_document['source_url_id'])
            if source_url:
                logger.info(f"Redirecting gated PDF {pdf_document['id']} to original URL: {source_url}")
                flash('Thank you for your submission. Redirecting to the original source.', 'success')
                return redirect(source_url)
        
        # Determine which file to serve: non-OCR version if available, otherwise original
        file_path = None
        download_filename = pdf_document.get('original_filename', 'document.pdf')
        
        # Check if we have a non-OCR version
        if pdf_document.get('has_non_ocr_version') and pdf_document.get('download_filename'):
            # Try to serve the non-OCR version - check multiple possible locations
            non_ocr_filename = pdf_document['download_filename']
            category = pdf_document['category']
            original_filename = pdf_document.get('filename', '')
            
            # Possible locations for non-OCR file
            possible_non_ocr_paths = []
            
            # 1. Hierarchical structure: category/pdf_base_name/non_ocr_filename
            if original_filename:
                pdf_base_name = os.path.splitext(original_filename)[0]
                # For non-OCR files, use the base name without non_ocr_ prefix
                if non_ocr_filename.startswith('non_ocr_'):
                    pdf_base_name = non_ocr_filename[8:]  # Remove 'non_ocr_' prefix
                    pdf_base_name = os.path.splitext(pdf_base_name)[0]
                hierarchical_path = os.path.join(TEMP_FOLDER, category, pdf_base_name, non_ocr_filename)
                possible_non_ocr_paths.append(("hierarchical structure", hierarchical_path))
            
            # 2. Flat structure: category/non_ocr_filename
            flat_path = os.path.join(TEMP_FOLDER, category, non_ocr_filename)
            possible_non_ocr_paths.append(("flat structure", flat_path))
            
            # Check each possible path
            for path_name, non_ocr_path in possible_non_ocr_paths:
                non_ocr_path = os.path.abspath(non_ocr_path)
                logger.info(f"Checking non-OCR file at {path_name}: {non_ocr_path}")
                
                if os.path.exists(non_ocr_path):
                    file_path = non_ocr_path
                    logger.info(f"Found non-OCR version at {path_name}: {file_path}")
                    
                    # Add indicator to filename that this is the clean version
                    base_name, ext = os.path.splitext(download_filename)
                    download_filename = f"{base_name}_clean{ext}"
                    break
            
            if not file_path:
                logger.warning(f"Non-OCR version not found at any location. Checked: {[p[1] for p in possible_non_ocr_paths]}")
        
        # Fallback to original file if non-OCR version not available
        if not file_path:
            filename = pdf_document.get('filename', '')
            category = pdf_document.get('category', '')
            
            if filename and category:
                # Try multiple possible locations for the original file
                possible_original_paths = []
                
                # 1. Hierarchical structure: category/pdf_base_name/filename
                pdf_base_name = os.path.splitext(filename)[0]
                # For non-OCR files, use the base name without non_ocr_ prefix
                if filename.startswith('non_ocr_'):
                    pdf_base_name = filename[8:]  # Remove 'non_ocr_' prefix
                    pdf_base_name = os.path.splitext(pdf_base_name)[0]
                hierarchical_path = os.path.join(TEMP_FOLDER, category, pdf_base_name, filename)
                possible_original_paths.append(("hierarchical structure", hierarchical_path))
                
                # 2. Flat structure: category/filename
                flat_path = os.path.join(TEMP_FOLDER, category, filename)
                possible_original_paths.append(("flat structure", flat_path))
                
                # Check each possible path
                for path_name, original_path in possible_original_paths:
                    original_path = os.path.abspath(original_path)
                    logger.info(f"Checking original file at {path_name}: {original_path}")
                    
                    if os.path.exists(original_path):
                        file_path = original_path
                        logger.info(f"Found original file at {path_name}: {file_path}")
                        break
                
                if not file_path:
                    logger.warning(f"Original file not found at any location. Checked: {[p[1] for p in possible_original_paths]}")
        
        # If still not found, try searching across all categories (legacy fallback)
        if not file_path:
            filename = pdf_document.get('filename') or pdf_document.get('original_filename', '')
            if filename:
                logger.info(f"Searching for file across all categories: {filename}")
                
                # Check current category and all available categories
                current_category = pdf_document.get('category', '')
                all_categories = list_categories()
                
                # Start with current category, then check all others
                categories_to_check = [current_category] + [cat for cat in all_categories if cat != current_category]
                
                for category in categories_to_check:
                    if category:  # Skip empty category
                        # Try both structures for each category
                        pdf_base_name = os.path.splitext(filename)[0]
                        # For non-OCR files, use the base name without non_ocr_ prefix
                        if filename.startswith('non_ocr_'):
                            pdf_base_name = filename[8:]  # Remove 'non_ocr_' prefix
                            pdf_base_name = os.path.splitext(pdf_base_name)[0]
                        
                        possible_paths = [
                            os.path.join(TEMP_FOLDER, category, pdf_base_name, filename),  # Hierarchical structure
                            os.path.join(TEMP_FOLDER, category, filename)  # Flat structure
                        ]
                        
                        for possible_path in possible_paths:
                            possible_path = os.path.abspath(possible_path)
                            if os.path.exists(possible_path):
                                file_path = possible_path
                                logger.info(f"Found file at: {file_path}")
                                break
                        
                        if file_path:
                            break
        
        # If we found a file, serve it
        if file_path and os.path.exists(file_path):
            logger.info(f"Serving file: {file_path} as {download_filename}")
            
            # Log conversion info if this is a non-OCR version
            if pdf_document.get('has_non_ocr_version') and 'non_ocr_' in os.path.basename(file_path):
                logger.info(f"Serving converted non-OCR PDF for user download")
                
                # Parse conversion metadata if available
                conversion_settings = pdf_document.get('conversion_settings')
                if conversion_settings:
                    try:
                        import json
                        metadata = json.loads(conversion_settings)
                        logger.info(f"Conversion metadata: DPI={metadata.get('conversion_dpi', 'unknown')}, "
                                  f"Pages={metadata.get('conversion_metadata', {}).get('pages_converted', 'unknown')}")
                    except:
                        pass
            
            return send_file(
                file_path,
                as_attachment=True,
                download_name=download_filename,
                mimetype='application/pdf'
            )
        else:
            logger.error(f"PDF file not found. Tried: {file_path}")
            flash('PDF file not found on server.', 'error')
            return '''
            <script>
                alert('PDF file not found on server.');
                window.close();
            </script>
            '''
            
    except Exception as e:
        logger.error(f"Error in handle_gated_download: {str(e)}")
        flash('An error occurred while downloading the PDF.', 'error')
        return '''
        <script>
            alert('An error occurred while downloading the PDF.');
            window.close();
        </script>
        '''

@app.route('/download_gated_pdf_by_id/<int:pdf_id>')
def download_gated_pdf_by_id(pdf_id):
    """
    Downloads a gated PDF after form submission verification.
    Implements smart routing: redirects to original URL if available, otherwise serves local copy.
    """
    try:
        # Get the PDF record
        pdf_record = get_pdf_by_id(pdf_id)
        if not pdf_record:
            flash('PDF not found.', 'error')
            return '''
            <script>
                alert('PDF not found.');
                window.close();
            </script>
            '''
        
        # Check if form submission exists
        submission_id = session.get(f'form_submission_{pdf_record["form_id"]}')
        if not submission_id:
            flash('Please complete the form before downloading.', 'error')
            return '''
            <script>
                alert('Please complete the form before downloading.');
                window.close();
            </script>
            '''
        
        # Verify the submission is still valid
        submission = get_form_submission(submission_id)
        if not submission:
            flash('Form submission not found or expired.', 'error')
            return '''
            <script>
                alert('Form submission not found or expired.');
                window.close();
            </script>
            '''
        
        # Check if submission is for the correct form
        if submission['form_id'] != pdf_record['form_id']:
            flash('Invalid form submission for this PDF.', 'error')
            return '''
            <script>
                alert('Invalid form submission for this PDF.');
                window.close();
            </script>
            '''
        
        # Smart routing: Check if we have an original URL
        if pdf_record.get('source_url_id'):
            # Get the source URL
            source_url = get_source_url_by_id(pdf_record['source_url_id'])
            if source_url:
                logger.info(f"Redirecting gated PDF {pdf_id} to original URL: {source_url}")
                flash('Thank you for your submission. Redirecting to the original source.', 'success')
                return redirect(source_url)
        
        # Fallback: Serve the local copy
        if submission_id:
            flash('Thank you for your submission. Here is your download.', 'success')
            
            # Get the file path
            file_path = os.path.join(TEMP_FOLDER, pdf_record['category'], pdf_record['filename'])
            
            if os.path.exists(file_path):
                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=pdf_record['original_filename'],
                    mimetype='application/pdf'
                )
            else:
                flash('PDF file not found on server.', 'error')
                return '''
                <script>
                    alert('PDF file not found on server.');
                    window.close();
                </script>
                '''
        else:
            flash('Please complete the form before downloading.', 'error')
            return '''
            <script>
                alert('Please complete the form before downloading.');
                window.close();
            </script>
            '''
            
    except Exception as e:
        logger.error(f"Error in download_gated_pdf: {str(e)}")
        flash('An error occurred while downloading the PDF.', 'error')
        return '''
        <script>
            alert('An error occurred while downloading the PDF.');
            window.close();
        </script>
        '''

def upload_gated_pdf(file, category, form_id, source_url=None, convert_to_non_ocr=False, conversion_dpi=300, keep_only_non_ocr=False):
    """
    Handles uploading a PDF that requires a form for download.
    Now supports OCR-to-non-OCR conversion for clean downloads.
    
    Args:
        file: The uploaded file object
        category: The category for the PDF
        form_id: The form ID required for download
        source_url: Optional source URL
        convert_to_non_ocr: Whether to convert OCR PDF to non-OCR version
        conversion_dpi: DPI for conversion (default: 300)
        keep_only_non_ocr: Whether to delete the original OCR file after conversion (default: False)
    
    Returns:
        tuple: (success, message)
    """
    try:
        # Import the conversion function
        from app.services.pdf_processor import convert_ocr_to_non_ocr_pdf, detect_ocr_pdf
        
        # 1. Generate filename but don't save yet - let embed_file_db_first handle the saving
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}"
        logger.info(f"Generated filename: {filename}")
        
        # We'll let embed_file_db_first handle the file saving to avoid duplicates
        
        # 2. Embed the file contents into ChromaDB first (this will save the file)
        try:
            success, message = embed_file_db_first(
                file,
                category,
                source_url=source_url,  # Pass the source URL
                use_vision=None,
                filter_sensitivity=None,
                max_images=None,
                force_update=False,
                original_filename=None,  # Let embed_file_db_first use the actual original filename for duplicate detection
                form_id=form_id  # Pass the form_id for gated PDFs
            )
            if not success:
                return False, f"Failed to embed PDF: {message}"
        except Exception as e:
            logger.error(f"Failed to embed PDF: {str(e)}")
            return False, f"Failed to embed PDF: {str(e)}"
        
        # 3. Now get the file path where it was saved
        from scripts.setup.create_temp_dirs import create_pdf_directory_structure
        dir_structure = create_pdf_directory_structure(category, filename)
        if not dir_structure:
            return False, f"Failed to locate saved PDF file"
        
        dest = dir_structure["pdf_path"]
        logger.info(f"PDF saved at: {dest}")
        
        # 4. Detect if PDF has OCR content and convert if requested
        download_filename = filename
        has_non_ocr_version = False
        conversion_metadata = None
        
        if convert_to_non_ocr:
            logger.info("OCR to non-OCR conversion requested")
            # First, detect if the PDF actually has OCR content
            ocr_detection = detect_ocr_pdf(dest)
            logger.info(f"OCR detection results: {ocr_detection}")
            if ocr_detection.get('is_ocr_pdf', False):
                non_ocr_filename = f"non_ocr_{filename}"
                non_ocr_path = os.path.join(dir_structure["pdf_dir"], non_ocr_filename)
                logger.info(f"Converting OCR PDF to non-OCR: {dest} -> {non_ocr_path}")
                success, message, metadata = convert_ocr_to_non_ocr_pdf(dest, non_ocr_path, dpi=conversion_dpi)
                if success:
                    download_filename = non_ocr_filename
                    has_non_ocr_version = True
                    conversion_metadata = {
                        'ocr_detection': ocr_detection,
                        'conversion_metadata': metadata,
                        'conversion_dpi': conversion_dpi,
                        'original_filename': filename,
                        'converted_filename': non_ocr_filename,
                        'original_deleted': False
                    }
                    logger.info(f"Successfully created non-OCR version: {non_ocr_filename}")
                    logger.info(f"Conversion metadata: {conversion_metadata}")
                    # --- MOVE FILE SIZE AND PAGE COUNT EXTRACTION HERE ---
                    try:
                        file_size = os.path.getsize(dest)
                        import fitz
                        doc = fitz.open(dest)
                        page_count = doc.page_count
                        doc.close()
                        logger.info(f"File size: {file_size} bytes, Page count: {page_count}")
                    except Exception as e:
                        logger.error(f"Failed to get file size or page count: {str(e)}")
                        file_size = 0
                        page_count = 0
                    # --- DELETE ORIGINAL OCR FILE AFTER METADATA EXTRACTION ---
                    if keep_only_non_ocr:
                        try:
                            if os.path.exists(dest):
                                logger.info(f"Updating vector database entries from {filename} to {non_ocr_filename}")
                                try:
                                    db = get_vector_db(category)
                                    all_docs = db.similarity_search_with_score("", k=1000, filter={"source": filename})
                                    if all_docs:
                                        logger.info(f"Found {len(all_docs)} vector entries to update")
                                        try:
                                            db.delete(where={"source": filename})
                                            logger.info(f"Deleted old vector entries for {filename}")
                                        except Exception as e:
                                            logger.error(f"Failed to delete old vector entries: {str(e)}")
                                        updated_docs = []
                                        for doc, score in all_docs:
                                            doc.metadata["source"] = non_ocr_filename
                                            updated_docs.append(doc)
                                        if updated_docs:
                                            db.add_documents(updated_docs)
                                            logger.info(f"Re-added {len(updated_docs)} vector entries with updated filename {non_ocr_filename}")
                                    else:
                                        logger.warning(f"No vector entries found for {filename}")
                                except Exception as e:
                                    logger.error(f"Failed to update vector database entries: {str(e)}")
                                os.remove(dest)
                                logger.info(f"Deleted original OCR file: {dest}")
                                conversion_metadata['original_deleted'] = True
                            else:
                                logger.warning(f"Original OCR file not found for deletion: {dest}")
                        except Exception as e:
                            logger.error(f"Failed to delete original OCR file {dest}: {str(e)}")
                else:
                    logger.warning(f"Failed to create non-OCR version: {message}")
                    logger.warning("Will use original PDF for download")
            else:
                logger.info("PDF does not contain significant OCR content, skipping conversion")
                conversion_metadata = {
                    'ocr_detection': ocr_detection,
                    'conversion_skipped': True,
                    'reason': 'No significant OCR content detected'
                }
        else:
            # --- IF NOT CONVERTING, EXTRACT METADATA AS BEFORE ---
            try:
                file_size = os.path.getsize(dest)
                import fitz
                doc = fitz.open(dest)
                page_count = doc.page_count
                doc.close()
                logger.info(f"File size: {file_size} bytes, Page count: {page_count}")
            except Exception as e:
                logger.error(f"Failed to get file size or page count: {str(e)}")
                file_size = 0
                page_count = 0

        # 6. Create a pdf_document record in the database with enhanced support
        try:
            pdf_id = create_gated_pdf_record(
                filename=filename,
                original_filename=file.filename,
                category=category,
                form_id=form_id,
                file_size=file_size,
                page_count=page_count,
                source_url=source_url,
                download_filename=download_filename,
                has_non_ocr_version=has_non_ocr_version,
                conversion_metadata=conversion_metadata
            )
            logger.info(f"PDF record created with ID: {pdf_id}")
            if has_non_ocr_version:
                logger.info(f"Non-OCR version will be served for downloads: {download_filename}")
        except Exception as e:
            logger.error(f"Failed to create PDF record: {str(e)}")
            return False, f"Failed to create PDF record: {str(e)}"

        # 7. Build success message
        result_message = f"Gated PDF uploaded successfully. Form ID: {form_id}"
        if has_non_ocr_version:
            if conversion_metadata and conversion_metadata.get('original_deleted'):
                result_message += f" Non-OCR version created and original OCR file deleted."
            else:
                result_message += f" Non-OCR version created for download."
        
        return True, result_message
            
    except Exception as e:
        logger.error(f"Error in upload_gated_pdf: {str(e)}")
        return False, f"Upload failed: {str(e)}"

@app.route('/<category>/<filename>')
@limiter.exempt
def serve_file(category, filename):
    """Serve PDF files directly from the app."""
    try:
        logger.info(f"[serve_file] category={category}, filename={filename}")
        
        # Ensure TEMP_FOLDER is an absolute path
        temp_folder_abs = os.path.abspath(TEMP_FOLDER)
        logger.info(f"[serve_file] TEMP_FOLDER (absolute): {temp_folder_abs}")
        
        # Try multiple path structures in order of preference
        possible_paths = []
        
        # 1. New structure: category/pdf_base_name/filename
        pdf_base_name = os.path.splitext(filename)[0]
        new_path = os.path.join(temp_folder_abs, category, pdf_base_name, filename)
        possible_paths.append(("new structure", new_path))
        
        # 2. Old structure: category/filename (direct)
        old_path = os.path.join(temp_folder_abs, category, filename)
        possible_paths.append(("old structure", old_path))
        
        # 3. Legacy _temp structure: _temp/category/filename
        legacy_path = os.path.join(temp_folder_abs, "_temp", category, filename)
        possible_paths.append(("legacy _temp", legacy_path))
        
        # Check each possible path
        for path_name, file_path in possible_paths:
            logger.info(f"[serve_file] Checking {path_name}: {file_path} (exists: {os.path.exists(file_path)})")
            if os.path.exists(file_path) and os.path.isfile(file_path):
                logger.info(f"[serve_file] Serving file from {path_name}: {file_path}")
                # Set the appropriate content type
                if filename.lower().endswith('.pdf'):
                    return send_file(file_path, mimetype='application/pdf')
                else:
                    return send_file(file_path)
        
        # If no file found, log all checked paths
        logger.warning(f"[serve_file] File not found: {filename} (category: {category})")
        logger.warning(f"[serve_file] Checked paths: {[p[1] for p in possible_paths]}")
        flash(f"File not found: {filename}", "error")
        return redirect(url_for('list_files'))
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        flash(f"Error serving file: {str(e)}", "error")
        return redirect(url_for('list_files'))

app.register_blueprint(public_api_bp)

if __name__ == '__main__':
    # Initialize the database
    db_utils.init_db()

    # Initialize GeoIP analytics database
    geoip_analytics.ensure_geoip_table()

    # Validate CSRF configuration
    from app.utils.config import validate_csrf_configuration
    csrf_validation = validate_csrf_configuration()
    
    if not csrf_validation['valid']:
        logger.error("CSRF configuration issues found:")
        for issue in csrf_validation['issues']:
            logger.error(f"  - {issue}")
    
    if csrf_validation['warnings']:
        logger.warning("CSRF configuration warnings:")
        for warning in csrf_validation['warnings']:
            logger.warning(f"  - {warning}")
    
    if csrf_validation['valid']:
        logger.info("CSRF configuration is valid")
    else:
        logger.error("CSRF configuration has issues that should be resolved")

    # Initialize the content database for database-first retrieval
    from app.models.schema import initialize_database, migrate_location_schema
    if not initialize_database():
        logger.error("Failed to initialize content database. Application may not function correctly.")
    else:
        logger.info("Content database initialized successfully.")

        # Run location schema migration
        if migrate_location_schema():
            logger.info("Location schema migration completed successfully.")
        else:
            logger.warning("Location schema migration failed, but application will continue.")

    # Initialize user management database
    if not um.init_user_db():
        logger.error("Failed to initialize user management database. User functionality may not work correctly.")
    else:
        logger.info("User management database initialized successfully.")

        # Initialize default permission groups and sync permissions
        from app.utils.permissions import ensure_default_permission_groups, sync_new_module_permissions

        if ensure_default_permission_groups():
            logger.info("Default permission groups initialized successfully.")
        else:
            logger.error("Failed to initialize default permission groups.")

        if sync_new_module_permissions():
            logger.info("Module permissions synchronized successfully.")
        else:
            logger.error("Failed to synchronize module permissions.")

    # Create temporary directories for PDF images and tables
    create_temp_directories()
    
    # Print all registered routes and their endpoints at startup
    for rule in app.url_map.iter_rules():
        print(f"Route: {rule} -> {rule.endpoint}")
    # Start the Flask application
    app.run(host='0.0.0.0', port=8080, debug=True)

