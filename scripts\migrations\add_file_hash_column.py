#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration script to add file_hash column to pdf_documents table for duplicate detection
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from app.utils.content_db import get_db_connection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_file_hash_column():
    """Add file_hash column to pdf_documents table for duplicate detection."""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check if column already exists
            cursor.execute("PRAGMA table_info(pdf_documents)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'file_hash' not in columns:
                logger.info("Adding file_hash column to pdf_documents table...")
                cursor.execute("""
                    ALTER TABLE pdf_documents 
                    ADD COLUMN file_hash TEXT
                """)
                
                # Create index for faster hash lookups
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pdf_documents_file_hash 
                    ON pdf_documents(file_hash)
                """)
                
                # Create composite index for hash + category lookups
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pdf_documents_hash_category 
                    ON pdf_documents(file_hash, category)
                """)
                
                conn.commit()
                logger.info("✓ Added file_hash column and indexes")
                
                # Optionally calculate hashes for existing files
                calculate_existing_hashes = input("Calculate hashes for existing PDF files? (y/N): ").lower().strip()
                if calculate_existing_hashes == 'y':
                    calculate_hashes_for_existing_files(cursor, conn)
                
            else:
                logger.info("file_hash column already exists")
            
            # Show updated schema
            cursor.execute("PRAGMA table_info(pdf_documents)")
            columns = cursor.fetchall()
            logger.info("Updated pdf_documents table schema:")
            for col in columns:
                logger.info(f"  - {col[1]} ({col[2]})")
            
            return True
            
    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        return False

def calculate_hashes_for_existing_files(cursor, conn):
    """Calculate file hashes for existing PDF files."""
    try:
        from app.utils.content_db import calculate_file_hash
        from app.utils.helpers import TEMP_FOLDER
        
        # Get all PDF documents without hashes
        cursor.execute("""
            SELECT id, filename, category, original_filename 
            FROM pdf_documents 
            WHERE file_hash IS NULL
        """)
        
        pdfs_to_process = cursor.fetchall()
        logger.info(f"Found {len(pdfs_to_process)} PDFs to process")
        
        processed = 0
        errors = 0
        
        for pdf_id, filename, category, original_filename in pdfs_to_process:
            try:
                # Construct file path
                file_path = os.path.join(TEMP_FOLDER, category, filename)
                
                if os.path.exists(file_path):
                    # Calculate hash
                    file_hash = calculate_file_hash(file_path)
                    
                    if file_hash:
                        # Update database
                        cursor.execute("""
                            UPDATE pdf_documents 
                            SET file_hash = ?, updated_at = datetime('now')
                            WHERE id = ?
                        """, (file_hash, pdf_id))
                        
                        processed += 1
                        if processed % 10 == 0:
                            logger.info(f"Processed {processed}/{len(pdfs_to_process)} files...")
                    else:
                        logger.warning(f"Could not calculate hash for {file_path}")
                        errors += 1
                else:
                    logger.warning(f"File not found: {file_path}")
                    errors += 1
                    
            except Exception as e:
                logger.error(f"Error processing {filename}: {str(e)}")
                errors += 1
        
        # Commit all updates
        conn.commit()
        
        logger.info(f"Hash calculation complete: {processed} processed, {errors} errors")
        
    except Exception as e:
        logger.error(f"Error calculating hashes for existing files: {str(e)}")

def main():
    """Run the migration."""
    logger.info("Starting file_hash column migration...")
    
    if add_file_hash_column():
        logger.info("✅ Migration completed successfully!")
        return 0
    else:
        logger.error("❌ Migration failed!")
        return 1

if __name__ == "__main__":
    exit(main())
