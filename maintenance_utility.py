#!/usr/bin/env python3
"""
Maintenance Utility for ERDB System

This utility provides comprehensive maintenance operations using the SynchronizationService:
- System health checks
- Synchronization status reports
- Automatic cleanup of orphaned records and duplicates
- Preventive maintenance operations

Usage:
    python maintenance_utility.py --help
    python maintenance_utility.py --health-check
    python maintenance_utility.py --sync-report
    python maintenance_utility.py --cleanup --dry-run
    python maintenance_utility.py --full-maintenance
"""

import sys
import os
import argparse
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.append('.')
sys.path.append('app')

try:
    from app.services.synchronization_service import SynchronizationService
except ImportError:
    from services.synchronization_service import SynchronizationService

def print_header(title: str):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n--- {title} ---")

def format_sync_status(status_dict: dict):
    """Format synchronization status for display"""
    print(f"Database Records: {status_dict['database_records']}")
    print(f"Filesystem Files: {status_dict['filesystem_files']}")
    print(f"Synchronized Files: {status_dict['synchronized_files']}")
    print(f"Sync Percentage: {status_dict.get('sync_percentage', 0):.1f}%")
    print(f"Issues Found: {status_dict.get('issues_found', 0)}")

def health_check():
    """Perform system health check"""
    print_header("SYSTEM HEALTH CHECK")
    
    sync_service = SynchronizationService()
    health_status = sync_service.validate_system_health()
    
    print(f"Timestamp: {health_status['timestamp']}")
    print(f"Overall Health: {health_status['overall_health'].upper()}")
    
    print_section("Check Results")
    for check_name, result in health_status['checks'].items():
        status_symbol = "[PASS]" if result == "pass" else "[WARN]" if result == "warning" else "[FAIL]"
        print(f"{status_symbol} {check_name.replace('_', ' ').title()}: {result.upper()}")
    
    if health_status['issues']:
        print_section("Issues Found")
        for i, issue in enumerate(health_status['issues'], 1):
            print(f"{i}. {issue}")
    
    if health_status['recommendations']:
        print_section("Recommendations")
        for i, rec in enumerate(health_status['recommendations'], 1):
            print(f"{i}. {rec}")
    
    return health_status['overall_health'] == 'healthy'

def sync_report():
    """Generate synchronization report"""
    print_header("SYNCHRONIZATION REPORT")
    
    sync_service = SynchronizationService()
    report = sync_service.generate_sync_report()
    
    print(f"Report Generated: {report['timestamp']}")
    
    print_section("Summary")
    format_sync_status(report['summary'])
    
    print_section("Issues Breakdown")
    issues = report['issues']
    print(f"Orphaned Database Records: {issues['orphaned_db_records']}")
    print(f"Orphaned Filesystem Files: {issues['orphaned_fs_files']}")
    print(f"Duplicate Groups Found: {issues['duplicates_found']}")
    print(f"Errors: {len(issues['errors'])}")
    
    if report['details']['orphaned_db_records']:
        print_section("Orphaned Database Records")
        for record in report['details']['orphaned_db_records']:
            print(f"  ID: {record['id']}, File: {record['filename']}, Original: {record['original_filename']}")
    
    if report['details']['orphaned_fs_files']:
        print_section("Orphaned Filesystem Files")
        for fs_file in report['details']['orphaned_fs_files']:
            print(f"  {fs_file}")
    
    if report['details']['duplicates']:
        print_section("Duplicate Groups")
        for dup in report['details']['duplicates']:
            print(f"  Original: {dup['original_filename']} ({dup['count']} copies)")
            for record in dup['records']:
                print(f"    ID: {record['id']}, File: {record['filename']}, Created: {record['created_at']}")
    
    if report['recommendations']:
        print_section("Recommendations")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"{i}. {rec}")
    
    return report

def cleanup_system(dry_run: bool = True):
    """Clean up orphaned records and duplicates"""
    action_type = "DRY RUN" if dry_run else "CLEANUP"
    print_header(f"SYSTEM CLEANUP - {action_type}")
    
    sync_service = SynchronizationService()
    
    # Clean up orphaned records
    print_section("Orphaned Records Cleanup")
    orphaned_results = sync_service.cleanup_orphaned_records(dry_run=dry_run)
    
    print(f"Orphaned DB Records: {orphaned_results['orphaned_db_records_removed']}")
    print(f"Orphaned FS Files: {orphaned_results['orphaned_fs_files_removed']}")
    print(f"Orphaned Form Submissions: {orphaned_results['orphaned_form_submissions_removed']}")
    
    if orphaned_results['errors']:
        print("Errors:")
        for error in orphaned_results['errors']:
            print(f"  - {error}")
    
    # Clean up duplicates
    print_section("Duplicate Cleanup")
    duplicate_results = sync_service.cleanup_duplicates(dry_run=dry_run)
    
    print(f"Duplicate Groups Processed: {duplicate_results['duplicates_processed']}")
    print(f"Records Removed: {duplicate_results['records_removed']}")
    print(f"Files Removed: {duplicate_results['files_removed']}")
    print(f"Space Freed: {duplicate_results['space_freed_mb']:.2f} MB")
    
    if duplicate_results['errors']:
        print("Errors:")
        for error in duplicate_results['errors']:
            print(f"  - {error}")
    
    total_actions = (orphaned_results['orphaned_db_records_removed'] + 
                    orphaned_results['orphaned_fs_files_removed'] + 
                    duplicate_results['records_removed'] + 
                    duplicate_results['files_removed'])
    
    if dry_run:
        print(f"\n[SUMMARY] {total_actions} items would be cleaned up")
        print("Run without --dry-run to execute cleanup")
    else:
        print(f"\n[COMPLETED] {total_actions} items cleaned up")
    
    return orphaned_results, duplicate_results

def full_maintenance(dry_run: bool = False):
    """Perform full system maintenance"""
    action_type = "DRY RUN" if dry_run else "MAINTENANCE"
    print_header(f"FULL SYSTEM MAINTENANCE - {action_type}")
    
    sync_service = SynchronizationService()
    maintenance_results = sync_service.perform_maintenance(dry_run=dry_run)
    
    print(f"Maintenance Started: {maintenance_results['timestamp']}")
    print(f"Actions Performed: {', '.join(maintenance_results['actions_performed'])}")
    
    print_section("Initial Status")
    format_sync_status(maintenance_results['initial_status'])
    
    if 'orphaned_cleanup' in maintenance_results:
        print_section("Orphaned Cleanup Results")
        orphaned = maintenance_results['orphaned_cleanup']
        print(f"DB Records Removed: {orphaned['orphaned_db_records_removed']}")
        print(f"FS Files Removed: {orphaned['orphaned_fs_files_removed']}")
        print(f"Form Submissions Removed: {orphaned['orphaned_form_submissions_removed']}")
    
    if 'duplicate_cleanup' in maintenance_results:
        print_section("Duplicate Cleanup Results")
        duplicates = maintenance_results['duplicate_cleanup']
        print(f"Duplicate Groups Processed: {duplicates['duplicates_processed']}")
        print(f"Records Removed: {duplicates['records_removed']}")
        print(f"Files Removed: {duplicates['files_removed']}")
        print(f"Space Freed: {duplicates['space_freed_mb']:.2f} MB")
    
    print_section("Final Status")
    format_sync_status(maintenance_results['final_status'])
    
    print_section("Improvements")
    improvements = maintenance_results['improvements']
    print(f"Issues Resolved: {improvements['issues_resolved']}")
    print(f"Sync Improvement: {improvements['sync_improvement']:.1f}%")
    
    if maintenance_results['total_errors'] > 0:
        print(f"\n[WARNING] {maintenance_results['total_errors']} errors occurred during maintenance")
    else:
        print(f"\n[SUCCESS] Maintenance completed successfully!")
    
    return maintenance_results

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="ERDB System Maintenance Utility")
    parser.add_argument('--health-check', action='store_true', help='Perform system health check')
    parser.add_argument('--sync-report', action='store_true', help='Generate synchronization report')
    parser.add_argument('--cleanup', action='store_true', help='Clean up orphaned records and duplicates')
    parser.add_argument('--full-maintenance', action='store_true', help='Perform full system maintenance')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    parser.add_argument('--json-output', action='store_true', help='Output results in JSON format')
    
    args = parser.parse_args()
    
    if not any([args.health_check, args.sync_report, args.cleanup, args.full_maintenance]):
        parser.print_help()
        return
    
    results = {}
    
    try:
        if args.health_check:
            if args.json_output:
                sync_service = SynchronizationService()
                results['health_check'] = sync_service.validate_system_health()
            else:
                health_check()
        
        if args.sync_report:
            if args.json_output:
                sync_service = SynchronizationService()
                results['sync_report'] = sync_service.generate_sync_report()
            else:
                sync_report()
        
        if args.cleanup:
            if args.json_output:
                sync_service = SynchronizationService()
                orphaned = sync_service.cleanup_orphaned_records(dry_run=args.dry_run)
                duplicates = sync_service.cleanup_duplicates(dry_run=args.dry_run)
                results['cleanup'] = {'orphaned': orphaned, 'duplicates': duplicates}
            else:
                cleanup_system(dry_run=args.dry_run)
        
        if args.full_maintenance:
            if args.json_output:
                sync_service = SynchronizationService()
                results['full_maintenance'] = sync_service.perform_maintenance(dry_run=args.dry_run)
            else:
                full_maintenance(dry_run=args.dry_run)
        
        if args.json_output:
            print(json.dumps(results, indent=2, default=str))
    
    except Exception as e:
        print(f"[ERROR] {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
