#!/usr/bin/env python3
"""
Migration script to fix original_filename values in the database.

This script fixes the bug where original_filename was incorrectly set to the timestamped filename
instead of the actual original filename.

Pattern to fix:
- filename: "20250716092442_canopy_v44n2.pdf" (correct - timestamped)
- original_filename: "20250716092442_canopy_v44n2.pdf" (wrong - should be "canopy_v44n2.pdf")

The script will:
1. Find all records where original_filename matches the timestamped pattern
2. Extract the actual original filename by removing the timestamp prefix
3. Update the original_filename to the correct value
"""

import sqlite3
import re
from datetime import datetime

def fix_original_filenames():
    """Fix original_filename values in the database"""
    print("🔧 Fixing original_filename values in database")
    print("=" * 50)
    
    try:
        # Connect to database
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Find all records where original_filename has timestamp pattern
        print("📊 Analyzing database records...")
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            WHERE original_filename LIKE '________%_%'
            AND original_filename = filename
        ''')
        
        records = cursor.fetchall()
        print(f"Found {len(records)} records that need fixing")
        print()
        
        if not records:
            print("✅ No records need fixing!")
            return
        
        # Process each record
        fixed_count = 0
        for record in records:
            record_id, filename, original_filename, category = record
            
            # Extract the actual original filename by removing timestamp prefix
            # Pattern: "20250716092442_canopy_v44n2.pdf" -> "canopy_v44n2.pdf"
            match = re.match(r'(\d{14})_(.+)', original_filename)
            if match:
                timestamp, actual_original = match.groups()
                
                print(f"🔄 Fixing record ID {record_id}:")
                print(f"   Category: {category}")
                print(f"   Filename: {filename}")
                print(f"   Original filename (old): {original_filename}")
                print(f"   Original filename (new): {actual_original}")
                
                # Update the record
                cursor.execute('''
                    UPDATE pdf_documents
                    SET original_filename = ?
                    WHERE id = ?
                ''', (actual_original, record_id))
                
                fixed_count += 1
                print(f"   ✅ Fixed!")
                print()
            else:
                print(f"⚠️  Skipping record ID {record_id}: couldn't parse filename pattern")
                print(f"   Filename: {filename}")
                print(f"   Original filename: {original_filename}")
                print()
        
        # Commit changes
        conn.commit()
        print(f"✅ Successfully fixed {fixed_count} records")
        
        # Verify the changes
        print("\n🔍 Verifying changes...")
        cursor.execute('''
            SELECT id, filename, original_filename, category
            FROM pdf_documents
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        updated_records = cursor.fetchall()
        print("Recent records after fix:")
        for record in updated_records:
            record_id, filename, original_filename, category = record
            print(f"   ID={record_id}, filename={filename}, original_filename={original_filename}, category={category}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        if conn:
            conn.close()
    
    print("\n🏁 Migration completed!")
    print("=" * 50)

def test_duplicate_detection_after_fix():
    """Test duplicate detection after the fix"""
    print("\n🧪 Testing duplicate detection after fix")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Find a record to test with
        cursor.execute('''
            SELECT original_filename, category
            FROM pdf_documents
            WHERE original_filename NOT LIKE '________%_%'
            LIMIT 1
        ''')
        
        record = cursor.fetchone()
        if record:
            original_filename, category = record
            print(f"📄 Testing with: {original_filename} in category {category}")
            
            # Test the query that duplicate detection uses
            cursor.execute('''
                SELECT id, filename, original_filename, category
                FROM pdf_documents
                WHERE original_filename = ? AND category = ?
                ORDER BY created_at DESC
                LIMIT 1
            ''', (original_filename, category))
            
            result = cursor.fetchone()
            if result:
                print("✅ Duplicate detection query works!")
                print(f"   Found: ID={result[0]}, filename={result[1]}, original_filename={result[2]}, category={result[3]}")
            else:
                print("❌ Duplicate detection query failed!")
        else:
            print("⚠️  No suitable records found for testing")
            
    except Exception as e:
        print(f"❌ Error testing: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_original_filenames()
    test_duplicate_detection_after_fix()
