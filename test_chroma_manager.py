#!/usr/bin/env python3
"""
Test script for ChromaDB Manager to verify it prevents instance conflicts.
"""

import os
import sys
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.chroma_manager import get_chroma_manager, get_unified_chroma_db, reset_chroma_instance
from app.services.vector_db import get_vector_db as get_vector_db_old
from app.services.optimized_vector_db import get_vector_db as get_vector_db_optimized
from app.services.unified_vector_db import get_unified_vector_db_service

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_singleton_behavior():
    """Test that the ChromaDB manager maintains singleton behavior."""
    logger.info("Testing singleton behavior...")
    
    manager1 = get_chroma_manager()
    manager2 = get_chroma_manager()
    
    assert manager1 is manager2, "ChromaDB manager should be a singleton"
    logger.info("✓ Singleton behavior verified")

def test_unified_instance():
    """Test that all services return the same ChromaDB instance."""
    logger.info("Testing unified instance across services...")
    
    # Reset to ensure clean state
    reset_chroma_instance()
    
    try:
        # Get instances from different services
        db1 = get_unified_chroma_db()
        db2 = get_vector_db_old("test_category")
        db3 = get_vector_db_optimized("test_category")
        
        # They should all point to the same underlying collection
        assert db1._collection.name == db2._collection.name == db3._collection.name
        logger.info("✓ All services use the same collection name")
        
        # Check persist directory
        assert db1._persist_directory == db2._persist_directory == db3._persist_directory
        logger.info("✓ All services use the same persist directory")
        
    except Exception as e:
        logger.error(f"✗ Unified instance test failed: {e}")
        raise

def test_concurrent_access():
    """Test concurrent access to ChromaDB manager."""
    logger.info("Testing concurrent access...")
    
    # Reset to ensure clean state
    reset_chroma_instance()
    
    def get_db_instance(thread_id):
        """Function to be run in multiple threads."""
        try:
            logger.info(f"Thread {thread_id}: Getting ChromaDB instance")
            db = get_unified_chroma_db()
            logger.info(f"Thread {thread_id}: Successfully got ChromaDB instance")
            return db, thread_id
        except Exception as e:
            logger.error(f"Thread {thread_id}: Failed to get ChromaDB instance: {e}")
            raise
    
    # Run multiple threads concurrently
    num_threads = 5
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(get_db_instance, i) for i in range(num_threads)]
        
        results = []
        for future in as_completed(futures):
            try:
                db, thread_id = future.result()
                results.append((db, thread_id))
            except Exception as e:
                logger.error(f"Thread failed: {e}")
                raise
    
    # Verify all threads got instances
    assert len(results) == num_threads, f"Expected {num_threads} results, got {len(results)}"
    
    # Verify all instances point to the same collection
    first_collection_name = results[0][0]._collection.name
    for db, thread_id in results:
        assert db._collection.name == first_collection_name, f"Thread {thread_id} got different collection"
    
    logger.info("✓ Concurrent access test passed")

def test_error_recovery():
    """Test error recovery and cleanup."""
    logger.info("Testing error recovery...")
    
    try:
        # Reset to clean state
        reset_chroma_instance()
        
        # Get an instance
        db1 = get_unified_chroma_db()
        logger.info("✓ First instance created successfully")
        
        # Get another instance (should reuse the same one)
        db2 = get_unified_chroma_db()
        assert db1._collection.name == db2._collection.name
        logger.info("✓ Second instance reused successfully")
        
        # Test reset functionality
        reset_chroma_instance()
        
        # Get a new instance after reset
        db3 = get_unified_chroma_db()
        logger.info("✓ Instance created successfully after reset")
        
    except Exception as e:
        logger.error(f"✗ Error recovery test failed: {e}")
        raise

def test_collection_info():
    """Test collection information retrieval."""
    logger.info("Testing collection info retrieval...")
    
    try:
        manager = get_chroma_manager()
        info = manager.get_collection_info()
        
        assert "name" in info, "Collection info should contain name"
        assert "count" in info, "Collection info should contain count"
        assert "model" in info, "Collection info should contain model"
        assert "persist_directory" in info, "Collection info should contain persist_directory"
        
        logger.info(f"✓ Collection info: {info}")
        
    except Exception as e:
        logger.error(f"✗ Collection info test failed: {e}")
        raise

def main():
    """Run all tests."""
    logger.info("Starting ChromaDB Manager tests...")
    
    try:
        test_singleton_behavior()
        test_unified_instance()
        test_concurrent_access()
        test_error_recovery()
        test_collection_info()
        
        logger.info("🎉 All tests passed!")
        
    except Exception as e:
        logger.error(f"❌ Tests failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
