#!/usr/bin/env python3
"""
Comprehensive test of the synchronization system

This test verifies that all components work together:
- Database records
- Filesystem files
- Web interface display
- Gated download system
- Duplicate detection
- Cleanup operations
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime

# Add the app directory to the Python path
sys.path.append('.')
sys.path.append('app')

def test_synchronization_service():
    """Test the SynchronizationService functionality"""
    print("🔄 TESTING SYNCHRONIZATION SERVICE")
    print("=" * 50)
    
    try:
        from app.services.synchronization_service import SynchronizationService
        sync_service = SynchronizationService()
        
        # Test 1: Health Check
        print("\n1. Testing system health check...")
        health_status = sync_service.validate_system_health()
        print(f"   Overall Health: {health_status['overall_health']}")
        
        for check_name, result in health_status['checks'].items():
            status_symbol = "✅" if result == "pass" else "⚠️" if result == "warning" else "❌"
            print(f"   {status_symbol} {check_name.replace('_', ' ').title()}: {result}")
        
        # Test 2: Synchronization Status
        print("\n2. Testing synchronization status...")
        sync_status = sync_service.check_synchronization_status()
        print(f"   Database Records: {sync_status.database_records}")
        print(f"   Filesystem Files: {sync_status.filesystem_files}")
        print(f"   Synchronized Files: {sync_status.synchronized_files}")
        print(f"   Orphaned DB Records: {len(sync_status.orphaned_db_records)}")
        print(f"   Orphaned FS Files: {len(sync_status.orphaned_fs_files)}")
        print(f"   Duplicates Found: {len(sync_status.duplicates_found)}")
        
        # Test 3: File Path Resolution
        print("\n3. Testing file path resolution...")
        db_files = sync_service.get_database_files()
        for db_file in db_files[:3]:  # Test first 3 files
            filename = db_file['filename']
            category = db_file['category']
            resolved_path = sync_service.resolve_file_path(filename, category)
            status = "✅ FOUND" if resolved_path else "❌ NOT FOUND"
            print(f"   {status}: {category}/{filename}")
            if resolved_path:
                print(f"      Path: {resolved_path}")
        
        # Test 4: Duplicate Detection
        print("\n4. Testing duplicate detection...")
        if db_files:
            test_file = db_files[0]
            duplicate_result = sync_service.detect_duplicates_before_upload(
                test_file['original_filename'], 
                test_file['category']
            )
            print(f"   Has Duplicates: {duplicate_result['has_duplicates']}")
            print(f"   Valid Duplicates: {len(duplicate_result['valid_duplicates'])}")
            print(f"   Orphaned Records: {len(duplicate_result['orphaned_records'])}")
            print(f"   Recommendation: {duplicate_result['recommendation']}")
        
        # Test 5: Generate Report
        print("\n5. Testing report generation...")
        report = sync_service.generate_sync_report()
        print(f"   Report Generated: {report['timestamp']}")
        print(f"   Sync Percentage: {report['summary']['sync_percentage']:.1f}%")
        print(f"   Issues Found: {report['summary']['issues_found']}")
        
        print("\n✅ SynchronizationService tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ SynchronizationService test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface_integration():
    """Test web interface integration"""
    print("\n🌐 TESTING WEB INTERFACE INTEGRATION")
    print("=" * 50)
    
    try:
        # Test the list_files function with the fixed logic
        import sys
        sys.path.append('.')
        
        # Test the simple list files logic we created earlier
        import sqlite3
        import os
        
        TEMP_FOLDER = 'data'
        
        # Get database records
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')
        db_records = cursor.fetchall()
        conn.close()
        
        print(f"1. Database records found: {len(db_records)}")
        
        # Test path resolution for each record
        files_found = 0
        files_data = {}
        
        for record in db_records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record
            
            # Test the same path logic as in the fixed function
            possible_paths = [
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, filename),
            ]
            
            fs_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    fs_path = path
                    files_found += 1
                    break
            
            if fs_path:
                if category not in files_data:
                    files_data[category] = []
                
                files_data[category].append({
                    "original_filename": original_filename,
                    "source": os.path.basename(fs_path),
                    "type": "pdf",
                    "database_id": pdf_id,
                    "created_at": created_at,
                    "file_size": file_size,
                    "page_count": page_count
                })
        
        print(f"2. Filesystem files found: {files_found}")
        print(f"3. Categories with files: {len(files_data)}")
        
        for category, files in files_data.items():
            print(f"   {category}: {len(files)} files")
            for file in files:
                print(f"     - {file['original_filename']}")
        
        # Test web interface display logic
        if files_data:
            print("\n✅ Web interface should display files correctly!")
            print("   The 'No files found' message should be gone.")
        else:
            print("\n❌ Web interface will still show 'No files found'")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ Web interface integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_maintenance_utility():
    """Test the maintenance utility"""
    print("\n🔧 TESTING MAINTENANCE UTILITY")
    print("=" * 50)
    
    try:
        # Test maintenance utility commands
        import subprocess
        
        # Test 1: Health Check
        print("1. Testing health check command...")
        result = subprocess.run(['python', 'maintenance_utility.py', '--health-check'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("   ✅ Health check command successful")
        else:
            print(f"   ❌ Health check command failed: {result.stderr}")
            return False
        
        # Test 2: Sync Report
        print("2. Testing sync report command...")
        result = subprocess.run(['python', 'maintenance_utility.py', '--sync-report'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("   ✅ Sync report command successful")
        else:
            print(f"   ❌ Sync report command failed: {result.stderr}")
            return False
        
        # Test 3: Cleanup (dry run)
        print("3. Testing cleanup command (dry run)...")
        result = subprocess.run(['python', 'maintenance_utility.py', '--cleanup', '--dry-run'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("   ✅ Cleanup dry run command successful")
        else:
            print(f"   ❌ Cleanup dry run command failed: {result.stderr}")
            return False
        
        print("\n✅ Maintenance utility tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Maintenance utility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gated_download_system():
    """Test gated download system integrity"""
    print("\n🔒 TESTING GATED DOWNLOAD SYSTEM")
    print("=" * 50)
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Test 1: Check forms
        cursor.execute('SELECT COUNT(*) FROM forms')
        forms_count = cursor.fetchone()[0]
        print(f"1. Forms in database: {forms_count}")
        
        # Test 2: Check gated PDFs
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE form_id IS NOT NULL')
        gated_pdfs_count = cursor.fetchone()[0]
        print(f"2. Gated PDFs: {gated_pdfs_count}")
        
        # Test 3: Check form submissions integrity
        cursor.execute('''
            SELECT COUNT(*) FROM form_submissions fs
            LEFT JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
            WHERE pd.id IS NULL
        ''')
        orphaned_submissions = cursor.fetchone()[0]
        print(f"3. Orphaned form submissions: {orphaned_submissions}")
        
        if orphaned_submissions == 0:
            print("   ✅ No orphaned form submissions found")
        else:
            print(f"   ⚠️  Found {orphaned_submissions} orphaned form submissions")
        
        # Test 4: Check valid submissions
        cursor.execute('''
            SELECT COUNT(*) FROM form_submissions fs
            JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
        ''')
        valid_submissions = cursor.fetchone()[0]
        print(f"4. Valid form submissions: {valid_submissions}")
        
        conn.close()
        
        print("\n✅ Gated download system tests completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Gated download system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive synchronization tests"""
    print("🧪 COMPREHENSIVE SYNCHRONIZATION SYSTEM TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")
    
    test_results = []
    
    # Run all tests
    test_results.append(("SynchronizationService", test_synchronization_service()))
    test_results.append(("Web Interface Integration", test_web_interface_integration()))
    test_results.append(("Maintenance Utility", test_maintenance_utility()))
    test_results.append(("Gated Download System", test_gated_download_system()))
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal Tests: {len(test_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Synchronization system is working correctly.")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
