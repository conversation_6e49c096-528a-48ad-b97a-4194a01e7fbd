#!/usr/bin/env python3
"""
Test the path matching logic for the fixed list_files function
"""

import os
import sqlite3

def test_path_matching():
    """Test if database files can be found in filesystem"""
    
    TEMP_FOLDER = 'data'
    
    print('=== TESTING PATH MATCHING LOGIC ===')
    
    # Get database records
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        cursor.execute('SELECT filename, category FROM pdf_documents ORDER BY created_at DESC')
        records = cursor.fetchall()
        conn.close()
        
        print(f'Found {len(records)} database records')
        
        matches_found = 0
        
        for filename, category in records:
            print(f'\nTesting: {filename} in {category}')
            
            # Test the exact path logic from our fix
            possible_paths = [
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                os.path.join(TEMP_FOLDER, category, filename),
            ]
            
            found = False
            for i, path in enumerate(possible_paths):
                exists = os.path.exists(path)
                if exists and not found:
                    print(f'  ✅ MATCH: {path}')
                    found = True
                    matches_found += 1
                    break
                elif i == 0:  # Only show first few attempts
                    print(f'  ❌ NOT FOUND: {path}')
            
            if not found:
                print(f'  🚨 NO MATCH FOUND for {filename}')
        
        print(f'\n=== RESULTS ===')
        print(f'Database records: {len(records)}')
        print(f'Filesystem matches: {matches_found}')
        print(f'Files that will be displayed: {matches_found}')
        
        if matches_found > 0:
            print('✅ SUCCESS: Files should now be visible in web interface!')
        else:
            print('❌ PROBLEM: No files will be displayed')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_path_matching()
