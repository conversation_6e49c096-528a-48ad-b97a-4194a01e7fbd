#!/usr/bin/env python3
"""
Test the fixed list_files function logic without Flask dependencies
"""

import os
import sqlite3

def get_db_connection():
    """Get a database connection with foreign keys enabled."""
    conn = sqlite3.connect('./erdb_main.db')
    conn.execute("PRAGMA foreign_keys = ON")
    conn.row_factory = sqlite3.Row  # Return rows as dictionaries
    return conn

def test_fixed_list_files_logic():
    """Test the core logic of the fixed list_files function"""
    print("🧪 Testing Fixed List Files Function Logic")
    print("=" * 60)
    
    try:
        # Get files from database first (same logic as the fixed function)
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, original_filename, category, created_at, file_size, page_count
            FROM pdf_documents
            ORDER BY category, created_at DESC
        ''')
        
        db_records = cursor.fetchall()
        conn.close()
        
        print(f"📊 Found {len(db_records)} files in database")
        
        # Group by category
        db_files_by_category = {}
        for record in db_records:
            pdf_id, filename, original_filename, category, created_at, file_size, page_count = record
            
            if category not in db_files_by_category:
                db_files_by_category[category] = []
            
            db_files_by_category[category].append({
                'id': pdf_id,
                'filename': filename,
                'original_filename': original_filename,
                'category': category,
                'created_at': created_at,
                'file_size': file_size,
                'page_count': page_count
            })
        
        # Process each category from database
        files_data = {}
        TEMP_FOLDER = 'data'
        
        for category, db_file_list in db_files_by_category.items():
            print(f"\n📁 Processing category: {category}")
            files = []
            
            for db_file in db_file_list:
                filename = db_file['filename']
                original_filename = db_file['original_filename']
                
                print(f"  📄 Processing: {original_filename}")
                print(f"     DB filename: {filename}")
                
                # Find filesystem path (same logic as fixed function)
                fs_path = None
                possible_paths = [
                    # New structure with subdirectories
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, 'temp', category, filename.replace('.pdf', ''), filename),
                    
                    # Legacy structure - direct files
                    os.path.join(TEMP_FOLDER, category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, category, filename),
                    os.path.join(TEMP_FOLDER, '_temp', category, f'non_ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, f'ocr_{filename}'),
                    os.path.join(TEMP_FOLDER, '_temp', category, filename),
                ]
                
                for path in possible_paths:
                    if os.path.exists(path):
                        fs_path = path
                        break
                
                if fs_path:
                    print(f"     ✅ Found filesystem file: {fs_path}")
                    
                    # Create file data structure (same as fixed function)
                    file_data = {
                        "original_filename": original_filename,
                        "source": os.path.basename(fs_path),
                        "type": "pdf",
                        "database_id": db_file['id'],
                        "created_at": db_file['created_at'],
                        "file_size": db_file['file_size'],
                        "page_count": db_file['page_count']
                    }
                    
                    files.append(file_data)
                else:
                    print(f"     ❌ No filesystem file found (will be skipped)")
            
            if files:
                files_data[category] = files
        
        # Summary
        print(f"\n📊 FINAL RESULTS:")
        print("=" * 60)
        
        total_files = sum(len(files) for files in files_data.values())
        print(f"Total categories: {len(files_data)}")
        print(f"Total valid files: {total_files}")
        
        for category, files in files_data.items():
            print(f"\n📁 {category}: {len(files)} files")
            for file_info in files:
                print(f"  ✅ {file_info['original_filename']}")
                print(f"     Source: {file_info['source']}")
                print(f"     DB ID: {file_info['database_id']}")
        
        # Check specifically for CANOPY_INTERNATIONAL
        canopy_files = files_data.get('CANOPY', [])
        canopy_international_files = [f for f in canopy_files if 'CANOPY_INTERNATIONAL' in f['original_filename']]
        
        print(f"\n🎯 CANOPY_INTERNATIONAL FILES:")
        print(f"   Found: {len(canopy_international_files)} files")
        
        if len(canopy_international_files) == 2:
            print("   🎉 PERFECT! Showing exactly 2 files as expected")
            print("   ✅ Fix is working correctly!")
        else:
            print(f"   ⚠️  Expected 2 files, but found {len(canopy_international_files)}")
        
        return files_data
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == "__main__":
    test_fixed_list_files_logic()
