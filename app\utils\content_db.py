import os
import sqlite3
import logging
import json
from datetime import datetime, timedelta
import time
import hashlib
from typing import Optional, Dict, Any

from app.models.schema import DB_PATH

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
URL_FRESHNESS_DAYS = 7  # Consider URL content stale after 7 days
MAX_DB_SIZE_MB = 5000  # 5GB maximum database size

def get_db_connection():
    """Get a database connection with foreign keys enabled."""
    conn = sqlite3.connect(DB_PATH)
    conn.execute("PRAGMA foreign_keys = ON")
    conn.row_factory = sqlite3.Row  # Return rows as dictionaries
    return conn

def get_source_url_by_url(url):
    """
    Get a source URL record by its URL.

    Args:
        url (str): The URL to look up

    Returns:
        dict: The source URL record or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM source_urls WHERE url = ?
        ''', (url,))

        row = cursor.fetchone()
        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting source URL by URL: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def insert_source_url(url, title=None, description=None, status='active', error_message=None):
    """
    Insert or update a source URL record.

    Args:
        url (str): The URL to insert or update
        title (str, optional): The page title
        description (str, optional): The page description
        status (str, optional): The URL status ('active', 'archived', 'error')
        error_message (str, optional): Error message if status is 'error'

    Returns:
        int: The ID of the inserted or updated record, or None on error
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Check if URL already exists
        cursor.execute("SELECT id FROM source_urls WHERE url = ?", (url,))
        row = cursor.fetchone()

        if row:
            # Update existing record
            cursor.execute('''
                UPDATE source_urls
                SET title = ?, description = ?, last_scraped = ?, last_updated = ?, status = ?, error_message = ?
                WHERE url = ?
            ''', (title, description, now, now, status, error_message, url))
            url_id = row['id']
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO source_urls (url, title, description, last_scraped, last_updated, status, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (url, title, description, now, now, status, error_message))
            url_id = cursor.lastrowid

        conn.commit()
        return url_id
    except sqlite3.Error as e:
        logger.error(f"Error inserting source URL: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def insert_url_content(source_url_id, content_type, content, content_order, metadata=None):
    """
    Insert content for a source URL.

    Args:
        source_url_id (int): The ID of the source URL
        content_type (str): The type of content ('text', 'image', 'link')
        content (str): The actual content
        content_order (int): The order/position of the content
        metadata (dict, optional): Additional metadata as a dictionary

    Returns:
        int: The ID of the inserted content, or None on error
    """
    try:
        # Validate content based on content_type
        if content_type in ('image', 'link'):
            # For images and links, ensure content is a valid URL string
            if not isinstance(content, str):
                logger.warning(f"Invalid {content_type} content (not a string): {content}")
                return None

            # Ensure URLs start with http:// or https://
            if not content.startswith(('http://', 'https://')):
                logger.warning(f"Invalid {content_type} URL (missing protocol): {content}")
                return None

            # Check for malformed URLs that might contain Python dict syntax
            if "'" in content or "{" in content or "}" in content or content.startswith("{'"):
                logger.warning(f"Potentially malformed {content_type} URL detected: {content[:200]}...")

                # Try to extract the actual URL if it's embedded in a dictionary-like string
                import re

                # First try to extract URL from dictionary-like string
                url_match = re.search(r'https?://[^\s\'"}]+', content)
                if url_match:
                    extracted_url = url_match.group(0)
                    logger.info(f"Extracted clean URL from malformed content: {extracted_url}")
                    content = extracted_url
                else:
                    # If no URL found, try to parse as JSON and extract URL
                    try:
                        import json
                        # Handle URL-encoded content
                        import urllib.parse
                        decoded_content = urllib.parse.unquote(content)

                        # Try to parse as JSON
                        if decoded_content.startswith('{') and decoded_content.endswith('}'):
                            parsed_dict = json.loads(decoded_content)
                            if isinstance(parsed_dict, dict) and 'url' in parsed_dict:
                                extracted_url = parsed_dict['url']
                                if isinstance(extracted_url, str) and extracted_url.startswith(('http://', 'https://')):
                                    logger.info(f"Extracted URL from JSON dictionary: {extracted_url}")
                                    content = extracted_url
                                else:
                                    logger.error(f"Invalid URL in dictionary: {extracted_url}")
                                    return None
                            else:
                                logger.error(f"No 'url' key found in dictionary: {parsed_dict}")
                                return None
                        else:
                            logger.error(f"Could not extract valid URL from malformed content: {content[:200]}...")
                            return None
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.error(f"Could not parse malformed URL content as JSON: {str(e)}")
                        return None

        conn = get_db_connection()
        cursor = conn.cursor()

        metadata_json = json.dumps(metadata) if metadata else None

        cursor.execute('''
            INSERT INTO url_content (source_url_id, content_type, content, content_order, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', (source_url_id, content_type, content, content_order, metadata_json))

        content_id = cursor.lastrowid
        conn.commit()
        return content_id
    except sqlite3.Error as e:
        logger.error(f"Error inserting URL content: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def get_url_images(source_url_id, limit=10):
    """
    Get images for a source URL.

    Args:
        source_url_id (int): The ID of the source URL
        limit (int, optional): Maximum number of images to return

    Returns:
        list: List of image URLs
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT content, metadata FROM url_content
            WHERE source_url_id = ? AND content_type = 'image'
            ORDER BY content_order
            LIMIT ?
        ''', (source_url_id, limit))

        rows = cursor.fetchall()
        images = []

        for row in rows:
            image_url = row['content']
            metadata = json.loads(row['metadata']) if row['metadata'] else {}
            images.append({
                'url': image_url,
                'metadata': metadata
            })

        return images
    except sqlite3.Error as e:
        logger.error(f"Error getting URL images: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def get_url_links(source_url_id, limit=10):
    """
    Get links for a source URL.

    Args:
        source_url_id (int): The ID of the source URL
        limit (int, optional): Maximum number of links to return

    Returns:
        list: List of link URLs
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT content, metadata FROM url_content
            WHERE source_url_id = ? AND content_type = 'link'
            ORDER BY content_order
            LIMIT ?
        ''', (source_url_id, limit))

        rows = cursor.fetchall()
        links = []

        for row in rows:
            link_url = row['content']
            metadata = json.loads(row['metadata']) if row['metadata'] else {}
            links.append({
                'url': link_url,
                'metadata': metadata
            })

        return links
    except sqlite3.Error as e:
        logger.error(f"Error getting URL links: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def get_url_text(source_url_id):
    """
    Get text content for a source URL.

    Args:
        source_url_id (int): The ID of the source URL

    Returns:
        str: The combined text content
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT content FROM url_content
            WHERE source_url_id = ? AND content_type = 'text'
            ORDER BY content_order
        ''', (source_url_id,))

        rows = cursor.fetchall()
        text_content = ' '.join([row['content'] for row in rows])

        return text_content
    except sqlite3.Error as e:
        logger.error(f"Error getting URL text: {str(e)}")
        return ""
    finally:
        if conn:
            conn.close()

def associate_pdf_with_url(pdf_filename, category, original_filename, source_url_id=None, published_year=None, published_month_start=None, published_month_end=None, published_month_range_str=None, file_hash=None):
    """
    Create or update a PDF document record and associate it with a source URL.
    Now supports publication date fields and file hash for duplicate detection.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        now = datetime.now().isoformat()

        # Ensure published_month_range_str is a string
        if published_month_range_str is None:
            published_month_range_str = ''
        else:
            published_month_range_str = str(published_month_range_str)

        # Calculate file hash if not provided
        if file_hash is None and pdf_filename:
            from app.utils.helpers import TEMP_FOLDER
            pdf_path = os.path.join(TEMP_FOLDER, category, pdf_filename)
            if os.path.exists(pdf_path):
                file_hash = calculate_file_hash(pdf_path)
                logger.debug(f"Calculated file hash for {pdf_filename}: {file_hash}")

        # Check if PDF already exists
        cursor.execute('''
            SELECT id FROM pdf_documents
            WHERE filename = ? AND category = ?
        ''', (pdf_filename, category))

        row = cursor.fetchone()

        if row:
            # Update existing record
            cursor.execute('''
                UPDATE pdf_documents
                SET original_filename = ?, source_url_id = ?, published_year = ?, published_month_start = ?, published_month_end = ?, published_month_range_str = ?, file_hash = ?, updated_at = datetime('now')
                WHERE id = ?
            ''', (original_filename, source_url_id, published_year, published_month_start, published_month_end, published_month_range_str, file_hash, row['id']))
            pdf_id = row['id']
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO pdf_documents (filename, original_filename, category, upload_date, source_url_id, published_year, published_month_start, published_month_end, published_month_range_str, file_hash, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            ''', (pdf_filename, original_filename, category, now, source_url_id, published_year, published_month_start, published_month_end, published_month_range_str, file_hash))
            pdf_id = cursor.lastrowid

        conn.commit()
        return pdf_id
    except sqlite3.Error as e:
        logger.error(f"Error associating PDF with URL: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def get_pdf_by_filename(filename, category):
    """
    Get a PDF document record by filename and category.

    Args:
        filename (str): The PDF filename
        category (str): The document category

    Returns:
        dict: The PDF document record or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pd.*, su.url as source_url
            FROM pdf_documents pd
            LEFT JOIN source_urls su ON pd.source_url_id = su.id
            WHERE pd.filename = ? AND pd.category = ?
        ''', (filename, category))

        row = cursor.fetchone()
        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting PDF by filename: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_pdf_by_filename_only(filename):
    """
    Get a PDF document record by filename only (without category requirement).
    Tries to match against `filename` first, then falls back to `original_filename`.

    Args:
        filename (str): The PDF filename (can be timestamped or original)

    Returns:
        dict: The PDF document record or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # First, try to find by the exact (potentially timestamped) filename
        cursor.execute('''
            SELECT pd.*, su.url as source_url
            FROM pdf_documents pd
            LEFT JOIN source_urls su ON pd.source_url_id = su.id
            WHERE pd.filename = ?
            ORDER BY pd.created_at DESC
            LIMIT 1
        ''', (filename,))

        row = cursor.fetchone()
        
        # If not found, try searching by original_filename
        if not row:
            cursor.execute('''
                SELECT pd.*, su.url as source_url
                FROM pdf_documents pd
                LEFT JOIN source_urls su ON pd.source_url_id = su.id
                WHERE pd.original_filename = ?
                ORDER BY pd.created_at DESC
                LIMIT 1
            ''', (filename,))
            row = cursor.fetchone()

        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting PDF by filename only: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_cover_image_for_pdf(pdf_id):
    """
    Get the cover image for a PDF document.

    Args:
        pdf_id (int): The ID of the PDF document

    Returns:
        dict: The cover image record or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM cover_images
            WHERE pdf_document_id = ?
        ''', (pdf_id,))

        row = cursor.fetchone()
        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting cover image for PDF: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def insert_cover_image(pdf_document_id, image_path, image_url, source, description=None):
    """
    Insert or update a cover image for a PDF document.

    Args:
        pdf_document_id (int): The ID of the PDF document
        image_path (str): The local filesystem path to the image
        image_url (str): The URL path for web access
        source (str): The source of the image ('pdf_first_page', 'pdf_internal', 'url', 'default')
        description (str, optional): A description of the image

    Returns:
        int: The ID of the cover image record, or None on error
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Validate source parameter
        valid_sources = ['pdf_first_page', 'pdf_internal', 'url', 'default']
        if source not in valid_sources:
            logger.error(f"Invalid cover image source: {source}")
            return None

        # Check if cover image already exists for this PDF
        cursor.execute('''
            SELECT id FROM cover_images
            WHERE pdf_document_id = ?
        ''', (pdf_document_id,))

        row = cursor.fetchone()

        if row:
            # Update existing record
            cursor.execute('''
                UPDATE cover_images
                SET image_path = ?, image_url = ?, source = ?, description = ?
                WHERE id = ?
            ''', (image_path, image_url, source, description, row['id']))
            cover_id = row['id']
        else:
            # Insert new record
            cursor.execute('''
                INSERT INTO cover_images (pdf_document_id, image_path, image_url, source, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (pdf_document_id, image_path, image_url, source, description))
            cover_id = cursor.lastrowid

        conn.commit()
        return cover_id
    except sqlite3.Error as e:
        logger.error(f"Error inserting cover image: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def is_url_content_fresh(url_record):
    """
    Check if URL content is fresh (less than URL_FRESHNESS_DAYS old).

    Args:
        url_record (dict): The source URL record

    Returns:
        bool: True if content is fresh, False otherwise
    """
    if not url_record or not url_record.get('last_scraped'):
        return False

    try:
        last_scraped = datetime.fromisoformat(url_record['last_scraped'])
        now = datetime.now()
        age = now - last_scraped

        return age.days < URL_FRESHNESS_DAYS
    except (ValueError, TypeError) as e:
        logger.error(f"Error checking URL freshness: {str(e)}")
        return False

def clean_stale_records(days_threshold=30):
    """
    Clean up stale records from the database.

    Args:
        days_threshold (int): Number of days to consider a record stale

    Returns:
        dict: Statistics about cleaned records
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Calculate cutoff date
        cutoff_date = (datetime.now() - timedelta(days=days_threshold)).isoformat()

        # Delete stale source URLs with status 'error'
        cursor.execute('''
            DELETE FROM source_urls
            WHERE status = 'error' AND last_updated < ?
        ''', (cutoff_date,))

        error_urls_deleted = cursor.rowcount

        # Archive old source URLs
        cursor.execute('''
            UPDATE source_urls
            SET status = 'archived'
            WHERE status = 'active' AND last_updated < ?
            AND id NOT IN (SELECT source_url_id FROM pdf_documents WHERE source_url_id IS NOT NULL)
        ''', (cutoff_date,))

        urls_archived = cursor.rowcount

        conn.commit()

        return {
            'error_urls_deleted': error_urls_deleted,
            'urls_archived': urls_archived
        }
    except sqlite3.Error as e:
        logger.error(f"Error cleaning stale records: {str(e)}")
        if conn:
            conn.rollback()
        return {
            'error_urls_deleted': 0,
            'urls_archived': 0,
            'error': str(e)
        }
    finally:
        if conn:
            conn.close()

def get_database_size():
    """
    Get the current size of the database in megabytes.

    Returns:
        float: Size of the database in MB
    """
    try:
        if not os.path.exists(DB_PATH):
            return 0

        size_bytes = os.path.getsize(DB_PATH)
        size_mb = size_bytes / (1024 * 1024)

        return size_mb
    except Exception as e:
        logger.error(f"Error getting database size: {str(e)}")
        return 0

def clean_malformed_urls():
    """
    Clean up malformed URLs in the database.

    This function scans the url_content table for image and link URLs that contain
    Python dictionary syntax or other invalid characters, and attempts to fix them.

    Returns:
        dict: Statistics about cleaned URLs
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Find potentially malformed URLs
        cursor.execute('''
            SELECT id, content_type, content
            FROM url_content
            WHERE content_type IN ('image', 'link')
            AND (content LIKE '%{%' OR content LIKE '%}%' OR content LIKE '%\'%')
        ''')

        rows = cursor.fetchall()

        fixed_count = 0
        deleted_count = 0

        import re
        import json
        import urllib.parse

        url_pattern = re.compile(r'https?://[^\s\'"}]+')

        for row in rows:
            row_id = row[0]
            content_type = row[1]
            content = row[2]

            fixed_url = None

            # Try multiple methods to extract a valid URL
            # Method 1: Direct regex extraction
            url_match = url_pattern.search(content)
            if url_match:
                fixed_url = url_match.group(0)
                logger.info(f"Method 1 - Extracted URL via regex: {fixed_url}")
            else:
                # Method 2: Try to parse as URL-encoded JSON
                try:
                    decoded_content = urllib.parse.unquote(content)
                    if decoded_content.startswith('{') and decoded_content.endswith('}'):
                        parsed_dict = json.loads(decoded_content)
                        if isinstance(parsed_dict, dict) and 'url' in parsed_dict:
                            potential_url = parsed_dict['url']
                            if isinstance(potential_url, str) and potential_url.startswith(('http://', 'https://')):
                                fixed_url = potential_url
                                logger.info(f"Method 2 - Extracted URL from JSON: {fixed_url}")
                except (json.JSONDecodeError, ValueError, UnicodeDecodeError):
                    pass

                # Method 3: Try to find URL in string representation of dictionary
                if not fixed_url:
                    url_in_dict_match = re.search(r"'url':\s*'(https?://[^']+)'", content)
                    if url_in_dict_match:
                        fixed_url = url_in_dict_match.group(1)
                        logger.info(f"Method 3 - Extracted URL from dict string: {fixed_url}")

            if fixed_url:
                # Validate the extracted URL
                if fixed_url.startswith(('http://', 'https://')) and len(fixed_url) > 10:
                    cursor.execute('''
                        UPDATE url_content
                        SET content = ?
                        WHERE id = ?
                    ''', (fixed_url, row_id))
                    fixed_count += 1
                    logger.info(f"Fixed malformed {content_type} URL: {content[:100]}... -> {fixed_url}")
                else:
                    # Invalid URL, delete it
                    cursor.execute('''
                        DELETE FROM url_content
                        WHERE id = ?
                    ''', (row_id,))
                    deleted_count += 1
                    logger.info(f"Deleted invalid {content_type} URL (invalid format): {content[:100]}...")
            else:
                # No valid URL found, delete the record
                cursor.execute('''
                    DELETE FROM url_content
                    WHERE id = ?
                ''', (row_id,))
                deleted_count += 1
                logger.info(f"Deleted invalid {content_type} URL (no URL found): {content[:100]}...")

        conn.commit()

        return {
            'fixed_count': fixed_count,
            'deleted_count': deleted_count,
            'total_processed': len(rows)
        }
    except sqlite3.Error as e:
        logger.error(f"Error cleaning malformed URLs: {str(e)}")
        if conn:
            conn.rollback()
        return {
            'fixed_count': 0,
            'deleted_count': 0,
            'total_processed': 0,
            'error': str(e)
        }
    finally:
        if conn:
            conn.close()

def calculate_file_hash(file_path):
    """
    Calculate SHA-256 hash of a file.

    Args:
        file_path (str): Path to the file

    Returns:
        str: Hex digest of the file hash
    """
    try:
        hasher = hashlib.sha256()
        with open(file_path, 'rb') as f:
            # Read and update hash in chunks to handle large files
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating file hash: {str(e)}")
        return None

def get_pdf_document_id(filename: str, category: str = None) -> Optional[int]:
    """
    Get the PDF document ID from the database by filename and optionally category.

    Args:
        filename: The PDF filename
        category: Optional category to filter by

    Returns:
        PDF document ID or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if category:
            cursor.execute("""
                SELECT id FROM pdf_documents
                WHERE filename = ? AND category = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (filename, category))
        else:
            cursor.execute("""
                SELECT id FROM pdf_documents
                WHERE filename = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (filename,))

        result = cursor.fetchone()
        return result['id'] if result else None

    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve PDF document ID: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def create_gated_pdf_record(filename, original_filename, category, form_id, file_size, page_count, source_url=None, download_filename=None, has_non_ocr_version=False, conversion_metadata=None, published_year=None, published_month_start=None, published_month_end=None, published_month_range_str=None, file_hash=None):
    """
    Creates a gated PDF record in the database, now with publication date fields.
    """
    from datetime import datetime
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Create source URL record if provided
            source_url_id = None
            if source_url:
                source_url_id = insert_source_url(source_url)
            
            now = datetime.now().isoformat(sep=' ', timespec='seconds')
            
            # Convert conversion metadata to JSON string if provided
            conversion_settings = json.dumps(conversion_metadata) if conversion_metadata else None
            
            # Use download_filename if provided, otherwise use original filename
            actual_download_filename = download_filename if download_filename else filename
            
            # Ensure published_month_range_str is a string
            if published_month_range_str is None:
                published_month_range_str = ''
            else:
                published_month_range_str = str(published_month_range_str)
            
            # Calculate file hash if not provided
            if file_hash is None and filename:
                from app.utils.helpers import TEMP_FOLDER
                pdf_path = os.path.join(TEMP_FOLDER, category, filename)
                if os.path.exists(pdf_path):
                    file_hash = calculate_file_hash(pdf_path)
                    logger.debug(f"Calculated file hash for gated PDF {filename}: {file_hash}")

            # Insert the PDF record
            cursor.execute("""
                INSERT INTO pdf_documents
                (filename, original_filename, category, form_id, file_size, page_count, source_url_id,
                 download_filename, has_non_ocr_version, conversion_settings, upload_date, created_at, updated_at,
                 published_year, published_month_start, published_month_end, published_month_range_str, file_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'), ?, ?, ?, ?, ?)
            """, (filename, original_filename, category, form_id, file_size, page_count, source_url_id,
                  actual_download_filename, has_non_ocr_version, conversion_settings, now,
                  published_year, published_month_start, published_month_end, published_month_range_str, file_hash))
            
            pdf_id = cursor.lastrowid
            conn.commit()
            
            logger.info(f"Created gated PDF record with ID {pdf_id}, form_id {form_id}, source_url_id {source_url_id}")
            logger.info(f"Non-OCR version: {has_non_ocr_version}, download_filename: {actual_download_filename}")
            return pdf_id
            
    except Exception as e:
        logger.error(f"Error creating gated PDF record: {str(e)}")
        raise

def get_pdf_by_id(pdf_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a PDF document by its ID.
    
    Args:
        pdf_id: The ID of the PDF document
    
    Returns:
        Dict containing PDF document data or None if not found
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT pd.*, su.url as source_url
                FROM pdf_documents pd
                LEFT JOIN source_urls su ON pd.source_url_id = su.id
                WHERE pd.id = ?
            """, (pdf_id,))
            
            row = cursor.fetchone()
            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            return None
            
    except Exception as e:
        logger.error(f"Error getting PDF by ID {pdf_id}: {str(e)}")
        return None

def get_source_url_by_id(source_url_id: int) -> Optional[str]:
    """
    Get a source URL by its ID.
    
    Args:
        source_url_id: The ID of the source URL
    
    Returns:
        The URL string or None if not found
    """
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("SELECT url FROM source_urls WHERE id = ?", (source_url_id,))
            row = cursor.fetchone()
            
            if row:
                return row[0]
            return None
            
    except Exception as e:
        logger.error(f"Error getting source URL by ID {source_url_id}: {str(e)}")
        return None

def get_pdf_by_original_filename(original_filename, category):
    """
    Get a PDF document record by its original filename and category.

    Args:
        original_filename (str): The original name of the PDF file.
        category (str): The category of the PDF file.

    Returns:
        dict: The PDF document record as a dictionary or None if not found.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        logger.info(f"[DB QUERY] Searching for original_filename='{original_filename}' AND category='{category}'")
        cursor.execute('''
            SELECT * FROM pdf_documents
            WHERE original_filename = ? AND category = ?
            ORDER BY created_at DESC
            LIMIT 1
        ''', (original_filename, category))
        row = cursor.fetchone()

        if row:
            result = dict(row)
            logger.info(f"[DB QUERY] ✅ Found record: ID={result.get('id')}, filename='{result.get('filename')}', original_filename='{result.get('original_filename')}', category='{result.get('category')}'")
            return result
        else:
            logger.info(f"[DB QUERY] ❌ No record found for original_filename='{original_filename}' AND category='{category}'")
            return None
    except sqlite3.Error as e:
        logger.error(f"Database error in get_pdf_by_original_filename: {e}")
        return None
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_pdf_by_hash(file_hash, category=None):
    """
    Get a PDF document record by its file hash.

    Args:
        file_hash (str): The SHA-256 hash of the PDF file.
        category (str, optional): The category to search in. If None, searches all categories.

    Returns:
        dict: The PDF document record as a dictionary or None if not found.
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if category:
            cursor.execute('''
                SELECT * FROM pdf_documents
                WHERE file_hash = ? AND category = ?
                ORDER BY created_at DESC
                LIMIT 1
            ''', (file_hash, category))
        else:
            cursor.execute('''
                SELECT * FROM pdf_documents
                WHERE file_hash = ?
                ORDER BY created_at DESC
                LIMIT 1
            ''', (file_hash,))

        row = cursor.fetchone()
        return dict(row) if row else None
    except sqlite3.Error as e:
        logger.error(f"Database error in get_pdf_by_hash: {e}")
        return None
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_pdf_by_filename_gated(filename):
    """
    Get a PDF document by its filename, ensuring it's a gated PDF.

    Args:
        filename (str): The PDF filename

    Returns:
        dict: The PDF document record or None if not found
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pd.*, su.url as source_url
            FROM pdf_documents pd
            LEFT JOIN source_urls su ON pd.source_url_id = su.id
            WHERE pd.filename = ? AND pd.category = 'gated'
        ''', (filename,))

        row = cursor.fetchone()
        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting gated PDF by filename: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()
