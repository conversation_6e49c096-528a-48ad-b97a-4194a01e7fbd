#!/usr/bin/env python3
"""
Comprehensive Synchronization Service for ERDB System

This service ensures data integrity and synchronization between:
- Database records (pdf_documents table)
- Filesystem files (data/temp/CATEGORY structure)
- Gated download forms and submissions
- Vector database entries
- Web interface display

Key Features:
- Centralized file path resolution
- Robust duplicate detection
- Automatic cleanup of orphaned records
- Health monitoring and reporting
- Preventive measures for future issues
"""

import os
import sqlite3
import shutil
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SyncStatus:
    """Data class for synchronization status"""
    database_records: int
    filesystem_files: int
    synchronized_files: int
    orphaned_db_records: List[Dict]
    orphaned_fs_files: List[str]
    duplicates_found: List[Dict]
    errors: List[str]
    last_sync: datetime

class SynchronizationService:
    """Comprehensive synchronization service for ERDB system"""
    
    def __init__(self, db_path: str = 'erdb_main.db', temp_folder: str = 'data'):
        self.db_path = db_path
        self.temp_folder = temp_folder
        self.logger = logger
        
    def get_db_connection(self) -> sqlite3.Connection:
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def resolve_file_path(self, filename: str, category: str) -> Optional[str]:
        """
        Centralized file path resolution - single source of truth
        
        Args:
            filename: Database filename (e.g., 20250717092343_CANOPY_INTERNATIONAL_VOL_1_NO_1.pdf)
            category: File category (e.g., CANOPY)
            
        Returns:
            Full path to file if found, None otherwise
        """
        possible_paths = [
            # Current structure: data/temp/CATEGORY/TIMESTAMP_FILENAME/non_ocr_TIMESTAMP_FILENAME.pdf
            os.path.join(self.temp_folder, 'temp', category, filename.replace('.pdf', ''), f'non_ocr_{filename}'),
            os.path.join(self.temp_folder, 'temp', category, filename.replace('.pdf', ''), f'ocr_{filename}'),
            os.path.join(self.temp_folder, 'temp', category, filename.replace('.pdf', ''), filename),
            
            # Legacy structure - direct files in category folder
            os.path.join(self.temp_folder, category, f'non_ocr_{filename}'),
            os.path.join(self.temp_folder, category, f'ocr_{filename}'),
            os.path.join(self.temp_folder, category, filename),
            
            # Alternative temp structure
            os.path.join(self.temp_folder, '_temp', category, f'non_ocr_{filename}'),
            os.path.join(self.temp_folder, '_temp', category, f'ocr_{filename}'),
            os.path.join(self.temp_folder, '_temp', category, filename),
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.path.isfile(path):
                self.logger.debug(f"Resolved file path: {filename} -> {path}")
                return path
        
        self.logger.warning(f"Could not resolve file path for: {category}/{filename}")
        return None
    
    def get_database_files(self) -> List[Dict]:
        """Get all PDF files from database"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at, 
                       file_size, page_count, form_id
                FROM pdf_documents
                ORDER BY category, created_at DESC
            ''')
            
            records = cursor.fetchall()
            conn.close()
            
            return [dict(record) for record in records]
            
        except Exception as e:
            self.logger.error(f"Error getting database files: {e}")
            return []
    
    def get_filesystem_files(self) -> List[Dict]:
        """Get all PDF files from filesystem"""
        filesystem_files = []
        
        try:
            for root, dirs, files in os.walk(self.temp_folder):
                for file in files:
                    if file.endswith('.pdf'):
                        full_path = os.path.join(root, file)
                        try:
                            size = os.path.getsize(full_path)
                            mtime = os.path.getmtime(full_path)
                            
                            filesystem_files.append({
                                'path': full_path,
                                'filename': file,
                                'size': size,
                                'modified': datetime.fromtimestamp(mtime),
                                'hash': self._calculate_file_hash(full_path)
                            })
                        except Exception as e:
                            self.logger.warning(f"Error processing file {full_path}: {e}")
            
            return filesystem_files
            
        except Exception as e:
            self.logger.error(f"Error scanning filesystem: {e}")
            return []
    
    def _calculate_file_hash(self, filepath: str) -> Optional[str]:
        """Calculate MD5 hash of a file"""
        try:
            hash_md5 = hashlib.md5()
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.warning(f"Error calculating hash for {filepath}: {e}")
            return None
    
    def check_synchronization_status(self) -> SyncStatus:
        """
        Comprehensive synchronization status check
        
        Returns:
            SyncStatus object with detailed sync information
        """
        self.logger.info("Checking synchronization status...")
        
        # Get data from both sources
        db_files = self.get_database_files()
        fs_files = self.get_filesystem_files()
        
        # Initialize tracking variables
        synchronized_files = 0
        orphaned_db_records = []
        orphaned_fs_files = []
        duplicates_found = []
        errors = []
        
        # Check database records against filesystem
        for db_file in db_files:
            filename = db_file['filename']
            category = db_file['category']
            
            # Try to resolve file path
            fs_path = self.resolve_file_path(filename, category)
            
            if fs_path:
                synchronized_files += 1
                self.logger.debug(f"Synchronized: {category}/{filename}")
            else:
                orphaned_db_records.append(db_file)
                self.logger.warning(f"Orphaned DB record: {category}/{filename}")
        
        # Check filesystem files against database
        db_filenames = {f['filename'] for f in db_files}
        
        for fs_file in fs_files:
            fs_filename = fs_file['filename']
            
            # Extract potential database filename from filesystem filename
            potential_db_names = [
                fs_filename,
                fs_filename.replace('non_ocr_', ''),
                fs_filename.replace('ocr_', '')
            ]
            
            found_in_db = any(name in db_filenames for name in potential_db_names)
            
            if not found_in_db:
                orphaned_fs_files.append(fs_file['path'])
                self.logger.warning(f"Orphaned FS file: {fs_file['path']}")
        
        # Check for duplicates by original filename
        original_filenames = {}
        for db_file in db_files:
            original = db_file['original_filename']
            if original in original_filenames:
                if original not in [d['original_filename'] for d in duplicates_found]:
                    duplicates_found.append({
                        'original_filename': original,
                        'records': [original_filenames[original], db_file]
                    })
                else:
                    # Add to existing duplicate group
                    for dup in duplicates_found:
                        if dup['original_filename'] == original:
                            dup['records'].append(db_file)
                            break
            else:
                original_filenames[original] = db_file
        
        return SyncStatus(
            database_records=len(db_files),
            filesystem_files=len(fs_files),
            synchronized_files=synchronized_files,
            orphaned_db_records=orphaned_db_records,
            orphaned_fs_files=orphaned_fs_files,
            duplicates_found=duplicates_found,
            errors=errors,
            last_sync=datetime.now()
        )
    
    def detect_duplicates_before_upload(self, original_filename: str, category: str) -> Dict[str, Any]:
        """
        Enhanced duplicate detection before upload
        
        Args:
            original_filename: Original name of file being uploaded
            category: Category for the file
            
        Returns:
            Dictionary with duplicate detection results
        """
        self.logger.info(f"Checking for duplicates: {original_filename} in {category}")
        
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Check for existing records with same original filename
            cursor.execute('''
                SELECT id, filename, original_filename, category, created_at, form_id
                FROM pdf_documents
                WHERE original_filename = ? AND category = ?
                ORDER BY created_at DESC
            ''', (original_filename, category))
            
            existing_records = cursor.fetchall()
            conn.close()
            
            if existing_records:
                # Check if filesystem files exist for these records
                valid_duplicates = []
                orphaned_records = []
                
                for record in existing_records:
                    fs_path = self.resolve_file_path(record['filename'], record['category'])
                    if fs_path:
                        valid_duplicates.append(dict(record))
                    else:
                        orphaned_records.append(dict(record))
                
                return {
                    'has_duplicates': len(valid_duplicates) > 0,
                    'valid_duplicates': valid_duplicates,
                    'orphaned_records': orphaned_records,
                    'total_found': len(existing_records),
                    'recommendation': 'replace' if len(valid_duplicates) == 1 else 'review'
                }
            
            return {
                'has_duplicates': False,
                'valid_duplicates': [],
                'orphaned_records': [],
                'total_found': 0,
                'recommendation': 'proceed'
            }
            
        except Exception as e:
            self.logger.error(f"Error in duplicate detection: {e}")
            return {
                'has_duplicates': False,
                'valid_duplicates': [],
                'orphaned_records': [],
                'total_found': 0,
                'recommendation': 'proceed',
                'error': str(e)
            }

    def cleanup_orphaned_records(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        Clean up orphaned database records and filesystem files

        Args:
            dry_run: If True, only report what would be cleaned up

        Returns:
            Dictionary with cleanup results
        """
        self.logger.info(f"Cleaning up orphaned records (dry_run: {dry_run})")

        sync_status = self.check_synchronization_status()

        cleanup_results = {
            'orphaned_db_records_removed': 0,
            'orphaned_fs_files_removed': 0,
            'orphaned_form_submissions_removed': 0,
            'errors': [],
            'dry_run': dry_run
        }

        if not dry_run:
            try:
                conn = self.get_db_connection()
                cursor = conn.cursor()

                # Clean up orphaned database records
                for record in sync_status.orphaned_db_records:
                    try:
                        cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (record['id'],))
                        cleanup_results['orphaned_db_records_removed'] += 1
                        self.logger.info(f"Removed orphaned DB record: {record['filename']}")
                    except Exception as e:
                        error_msg = f"Error removing DB record {record['id']}: {e}"
                        cleanup_results['errors'].append(error_msg)
                        self.logger.error(error_msg)

                # Clean up orphaned form submissions
                cursor.execute('''
                    DELETE FROM form_submissions
                    WHERE pdf_document_id NOT IN (SELECT id FROM pdf_documents)
                ''')
                cleanup_results['orphaned_form_submissions_removed'] = cursor.rowcount

                conn.commit()
                conn.close()

                # Clean up orphaned filesystem files
                for fs_path in sync_status.orphaned_fs_files:
                    try:
                        if os.path.exists(fs_path):
                            os.remove(fs_path)
                            cleanup_results['orphaned_fs_files_removed'] += 1
                            self.logger.info(f"Removed orphaned FS file: {fs_path}")

                            # Try to remove empty parent directory
                            parent_dir = os.path.dirname(fs_path)
                            if os.path.exists(parent_dir) and not os.listdir(parent_dir):
                                os.rmdir(parent_dir)
                                self.logger.info(f"Removed empty directory: {parent_dir}")

                    except Exception as e:
                        error_msg = f"Error removing FS file {fs_path}: {e}"
                        cleanup_results['errors'].append(error_msg)
                        self.logger.error(error_msg)

            except Exception as e:
                error_msg = f"Error during cleanup: {e}"
                cleanup_results['errors'].append(error_msg)
                self.logger.error(error_msg)
        else:
            # Dry run - just report what would be cleaned
            cleanup_results['orphaned_db_records_removed'] = len(sync_status.orphaned_db_records)
            cleanup_results['orphaned_fs_files_removed'] = len(sync_status.orphaned_fs_files)

            # Count orphaned form submissions
            try:
                conn = self.get_db_connection()
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM form_submissions
                    WHERE pdf_document_id NOT IN (SELECT id FROM pdf_documents)
                ''')
                cleanup_results['orphaned_form_submissions_removed'] = cursor.fetchone()[0]
                conn.close()
            except Exception as e:
                cleanup_results['errors'].append(f"Error counting orphaned submissions: {e}")

        return cleanup_results

    def cleanup_duplicates(self, keep_most_recent: bool = True, dry_run: bool = True) -> Dict[str, Any]:
        """
        Clean up duplicate files, keeping only one copy

        Args:
            keep_most_recent: If True, keep the most recent duplicate
            dry_run: If True, only report what would be cleaned up

        Returns:
            Dictionary with cleanup results
        """
        self.logger.info(f"Cleaning up duplicates (dry_run: {dry_run})")

        sync_status = self.check_synchronization_status()

        cleanup_results = {
            'duplicates_processed': 0,
            'records_removed': 0,
            'files_removed': 0,
            'space_freed_mb': 0.0,
            'errors': [],
            'dry_run': dry_run
        }

        for duplicate_group in sync_status.duplicates_found:
            records = duplicate_group['records']
            if len(records) <= 1:
                continue

            cleanup_results['duplicates_processed'] += 1

            # Sort by creation date (most recent first if keep_most_recent=True)
            sorted_records = sorted(records, key=lambda x: x['created_at'],
                                  reverse=keep_most_recent)

            keep_record = sorted_records[0]
            remove_records = sorted_records[1:]

            self.logger.info(f"Processing duplicates for: {duplicate_group['original_filename']}")
            self.logger.info(f"Keeping: {keep_record['filename']} (created: {keep_record['created_at']})")

            if not dry_run:
                try:
                    conn = self.get_db_connection()
                    cursor = conn.cursor()

                    for record in remove_records:
                        # Remove filesystem file
                        fs_path = self.resolve_file_path(record['filename'], record['category'])
                        if fs_path and os.path.exists(fs_path):
                            file_size = os.path.getsize(fs_path)
                            os.remove(fs_path)
                            cleanup_results['files_removed'] += 1
                            cleanup_results['space_freed_mb'] += file_size / (1024 * 1024)
                            self.logger.info(f"Removed duplicate file: {fs_path}")

                            # Remove empty parent directory
                            parent_dir = os.path.dirname(fs_path)
                            if os.path.exists(parent_dir) and not os.listdir(parent_dir):
                                os.rmdir(parent_dir)

                        # Remove database record
                        cursor.execute('DELETE FROM pdf_documents WHERE id = ?', (record['id'],))
                        cleanup_results['records_removed'] += 1
                        self.logger.info(f"Removed duplicate DB record: {record['filename']}")

                    conn.commit()
                    conn.close()

                except Exception as e:
                    error_msg = f"Error cleaning duplicate {duplicate_group['original_filename']}: {e}"
                    cleanup_results['errors'].append(error_msg)
                    self.logger.error(error_msg)
            else:
                # Dry run - calculate what would be removed
                for record in remove_records:
                    fs_path = self.resolve_file_path(record['filename'], record['category'])
                    if fs_path and os.path.exists(fs_path):
                        file_size = os.path.getsize(fs_path)
                        cleanup_results['files_removed'] += 1
                        cleanup_results['space_freed_mb'] += file_size / (1024 * 1024)
                    cleanup_results['records_removed'] += 1

        return cleanup_results

    def generate_sync_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive synchronization report

        Returns:
            Dictionary with detailed sync report
        """
        self.logger.info("Generating synchronization report...")

        sync_status = self.check_synchronization_status()

        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'database_records': sync_status.database_records,
                'filesystem_files': sync_status.filesystem_files,
                'synchronized_files': sync_status.synchronized_files,
                'sync_percentage': (sync_status.synchronized_files / max(sync_status.database_records, 1)) * 100,
                'issues_found': len(sync_status.orphaned_db_records) + len(sync_status.orphaned_fs_files) + len(sync_status.duplicates_found)
            },
            'issues': {
                'orphaned_db_records': len(sync_status.orphaned_db_records),
                'orphaned_fs_files': len(sync_status.orphaned_fs_files),
                'duplicates_found': len(sync_status.duplicates_found),
                'errors': sync_status.errors
            },
            'details': {
                'orphaned_db_records': [
                    {
                        'id': record['id'],
                        'filename': record['filename'],
                        'original_filename': record['original_filename'],
                        'category': record['category'],
                        'created_at': record['created_at']
                    } for record in sync_status.orphaned_db_records
                ],
                'orphaned_fs_files': sync_status.orphaned_fs_files,
                'duplicates': [
                    {
                        'original_filename': dup['original_filename'],
                        'count': len(dup['records']),
                        'records': [
                            {
                                'id': record['id'],
                                'filename': record['filename'],
                                'created_at': record['created_at']
                            } for record in dup['records']
                        ]
                    } for dup in sync_status.duplicates_found
                ]
            },
            'recommendations': self._generate_recommendations(sync_status)
        }

        return report

    def _generate_recommendations(self, sync_status: SyncStatus) -> List[str]:
        """Generate recommendations based on sync status"""
        recommendations = []

        if sync_status.orphaned_db_records:
            recommendations.append(f"Remove {len(sync_status.orphaned_db_records)} orphaned database records")

        if sync_status.orphaned_fs_files:
            recommendations.append(f"Remove {len(sync_status.orphaned_fs_files)} orphaned filesystem files")

        if sync_status.duplicates_found:
            total_duplicates = sum(len(dup['records']) - 1 for dup in sync_status.duplicates_found)
            recommendations.append(f"Clean up {total_duplicates} duplicate records")

        if sync_status.synchronized_files == sync_status.database_records and not sync_status.orphaned_fs_files:
            recommendations.append("System is fully synchronized - no action needed")

        if not recommendations:
            recommendations.append("Run full system health check")

        return recommendations

    def perform_maintenance(self, cleanup_orphaned: bool = True, cleanup_duplicates: bool = True,
                          dry_run: bool = False) -> Dict[str, Any]:
        """
        Perform comprehensive system maintenance

        Args:
            cleanup_orphaned: Whether to clean up orphaned records
            cleanup_duplicates: Whether to clean up duplicates
            dry_run: If True, only report what would be done

        Returns:
            Dictionary with maintenance results
        """
        self.logger.info(f"Performing system maintenance (dry_run: {dry_run})")

        maintenance_results = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'actions_performed': [],
            'total_errors': 0,
            'summary': {}
        }

        try:
            # Generate initial report
            initial_report = self.generate_sync_report()
            maintenance_results['initial_status'] = initial_report['summary']

            # Clean up orphaned records
            if cleanup_orphaned and (initial_report['issues']['orphaned_db_records'] > 0 or
                                   initial_report['issues']['orphaned_fs_files'] > 0):
                orphaned_results = self.cleanup_orphaned_records(dry_run=dry_run)
                maintenance_results['orphaned_cleanup'] = orphaned_results
                maintenance_results['actions_performed'].append('orphaned_cleanup')
                maintenance_results['total_errors'] += len(orphaned_results['errors'])

            # Clean up duplicates
            if cleanup_duplicates and initial_report['issues']['duplicates_found'] > 0:
                duplicate_results = self.cleanup_duplicates(dry_run=dry_run)
                maintenance_results['duplicate_cleanup'] = duplicate_results
                maintenance_results['actions_performed'].append('duplicate_cleanup')
                maintenance_results['total_errors'] += len(duplicate_results['errors'])

            # Generate final report
            final_report = self.generate_sync_report()
            maintenance_results['final_status'] = final_report['summary']

            # Calculate improvements
            maintenance_results['improvements'] = {
                'issues_resolved': (initial_report['summary']['issues_found'] -
                                  final_report['summary']['issues_found']),
                'sync_improvement': (final_report['summary']['sync_percentage'] -
                                   initial_report['summary']['sync_percentage'])
            }

            self.logger.info(f"Maintenance completed. Issues resolved: {maintenance_results['improvements']['issues_resolved']}")

        except Exception as e:
            error_msg = f"Error during maintenance: {e}"
            maintenance_results['errors'] = [error_msg]
            maintenance_results['total_errors'] += 1
            self.logger.error(error_msg)

        return maintenance_results

    def validate_system_health(self) -> Dict[str, Any]:
        """
        Validate overall system health

        Returns:
            Dictionary with health status
        """
        self.logger.info("Validating system health...")

        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_health': 'unknown',
            'checks': {},
            'issues': [],
            'recommendations': []
        }

        try:
            # Check database connectivity
            try:
                conn = self.get_db_connection()
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM pdf_documents')
                db_count = cursor.fetchone()[0]
                conn.close()
                health_status['checks']['database_connectivity'] = 'pass'
            except Exception as e:
                health_status['checks']['database_connectivity'] = 'fail'
                health_status['issues'].append(f"Database connectivity issue: {e}")

            # Check filesystem accessibility
            try:
                if os.path.exists(self.temp_folder) and os.access(self.temp_folder, os.R_OK | os.W_OK):
                    health_status['checks']['filesystem_access'] = 'pass'
                else:
                    health_status['checks']['filesystem_access'] = 'fail'
                    health_status['issues'].append(f"Filesystem access issue: {self.temp_folder}")
            except Exception as e:
                health_status['checks']['filesystem_access'] = 'fail'
                health_status['issues'].append(f"Filesystem check error: {e}")

            # Check synchronization status
            sync_status = self.check_synchronization_status()
            sync_percentage = (sync_status.synchronized_files / max(sync_status.database_records, 1)) * 100

            if sync_percentage >= 95:
                health_status['checks']['synchronization'] = 'pass'
            elif sync_percentage >= 80:
                health_status['checks']['synchronization'] = 'warning'
                health_status['issues'].append(f"Synchronization at {sync_percentage:.1f}% - some files may be missing")
            else:
                health_status['checks']['synchronization'] = 'fail'
                health_status['issues'].append(f"Poor synchronization: {sync_percentage:.1f}%")

            # Determine overall health
            failed_checks = sum(1 for check in health_status['checks'].values() if check == 'fail')
            warning_checks = sum(1 for check in health_status['checks'].values() if check == 'warning')

            if failed_checks == 0 and warning_checks == 0:
                health_status['overall_health'] = 'healthy'
            elif failed_checks == 0:
                health_status['overall_health'] = 'warning'
            else:
                health_status['overall_health'] = 'critical'

            # Generate recommendations
            if health_status['overall_health'] != 'healthy':
                health_status['recommendations'] = self._generate_health_recommendations(health_status)

        except Exception as e:
            health_status['overall_health'] = 'critical'
            health_status['issues'].append(f"Health check error: {e}")
            self.logger.error(f"Error during health check: {e}")

        return health_status

    def _generate_health_recommendations(self, health_status: Dict) -> List[str]:
        """Generate health recommendations based on status"""
        recommendations = []

        if health_status['checks'].get('database_connectivity') == 'fail':
            recommendations.append("Check database connection and permissions")

        if health_status['checks'].get('filesystem_access') == 'fail':
            recommendations.append("Check filesystem permissions and disk space")

        if health_status['checks'].get('synchronization') in ['fail', 'warning']:
            recommendations.append("Run maintenance to fix synchronization issues")

        if not recommendations:
            recommendations.append("System appears healthy - continue monitoring")

        return recommendations
