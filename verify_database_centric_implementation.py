#!/usr/bin/env python3
"""
Final Verification Script for Database-Centric Architecture

This script verifies that the database-centric architecture implementation
is complete and working correctly.
"""

import sqlite3
import os
from datetime import datetime

def verify_database_schema():
    """Verify database schema has all required columns"""
    print("🗄️  VERIFYING DATABASE SCHEMA")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Check table schema
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = cursor.fetchall()
        
        required_columns = {
            'file_hash': 'TEXT',
            'pdf_content_blob': 'BLOB', 
            'cover_image_blob': 'BLOB',
            'cover_image_format': 'TEXT'
        }
        
        existing_columns = {col[1]: col[2] for col in columns}
        
        print("Required columns verification:")
        all_present = True
        for col_name, col_type in required_columns.items():
            if col_name in existing_columns:
                print(f"  ✅ {col_name} ({existing_columns[col_name]})")
            else:
                print(f"  ❌ {col_name} MISSING")
                all_present = False
        
        # Check indexes
        cursor.execute("PRAGMA index_list(pdf_documents)")
        indexes = cursor.fetchall()
        
        print(f"\nIndexes found: {len(indexes)}")
        for index in indexes:
            print(f"  - {index[1]}")
        
        conn.close()
        return all_present
        
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def verify_data_migration():
    """Verify all data has been migrated to BLOBs"""
    print("\n📦 VERIFYING DATA MIGRATION")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Check total records
        cursor.execute('SELECT COUNT(*) FROM pdf_documents')
        total_count = cursor.fetchone()[0]
        
        # Check records with PDF content
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE pdf_content_blob IS NOT NULL')
        blob_count = cursor.fetchone()[0]
        
        # Check records with file hashes
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE file_hash IS NOT NULL')
        hash_count = cursor.fetchone()[0]
        
        # Check records with cover images
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE cover_image_blob IS NOT NULL')
        cover_count = cursor.fetchone()[0]
        
        print(f"Total records: {total_count}")
        print(f"Records with PDF content: {blob_count} ({(blob_count/max(total_count,1)*100):.1f}%)")
        print(f"Records with file hashes: {hash_count} ({(hash_count/max(total_count,1)*100):.1f}%)")
        print(f"Records with cover images: {cover_count} ({(cover_count/max(total_count,1)*100):.1f}%)")
        
        # Show sample data
        cursor.execute('''
            SELECT id, original_filename, category, file_size, page_count,
                   LENGTH(pdf_content_blob) as content_size,
                   LENGTH(cover_image_blob) as cover_size,
                   SUBSTR(file_hash, 1, 16) as hash_preview
            FROM pdf_documents 
            LIMIT 3
        ''')
        
        records = cursor.fetchall()
        print(f"\nSample records:")
        for record in records:
            print(f"  ID {record[0]}: {record[1]} ({record[2]})")
            print(f"    File size: {record[3]} bytes, Pages: {record[4]}")
            print(f"    Content BLOB: {record[5]} bytes")
            print(f"    Cover BLOB: {record[6]} bytes")
            print(f"    Hash: {record[7]}...")
        
        conn.close()
        
        migration_complete = (blob_count == total_count and hash_count == total_count and total_count > 0)
        return migration_complete, total_count, blob_count, hash_count, cover_count
        
    except Exception as e:
        print(f"❌ Error verifying data migration: {e}")
        return False, 0, 0, 0, 0

def verify_vector_database_removal():
    """Verify vector database has been removed"""
    print("\n🧹 VERIFYING VECTOR DATABASE REMOVAL")
    print("=" * 40)
    
    vector_paths = [
        'data/unified_chroma',
        'chroma_db',
        'data/chroma_db',
        'app/chroma_db',
        'chroma',
        'data/chroma'
    ]
    
    removed_count = 0
    for path in vector_paths:
        if os.path.exists(path):
            print(f"  ⚠️  {path} still exists")
        else:
            print(f"  ✅ {path} removed")
            removed_count += 1
    
    # Check for any remaining chroma files
    chroma_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if 'chroma' in file.lower() and file.endswith('.db'):
                chroma_files.append(os.path.join(root, file))
    
    if chroma_files:
        print(f"  ⚠️  Found {len(chroma_files)} remaining chroma files:")
        for file in chroma_files:
            print(f"    - {file}")
    else:
        print(f"  ✅ No remaining chroma database files")
    
    return len(chroma_files) == 0

def verify_implementation_files():
    """Verify implementation files are present"""
    print("\n📁 VERIFYING IMPLEMENTATION FILES")
    print("=" * 40)
    
    required_files = [
        'app/utils/database_centric_helpers.py',
        'database_migration_to_centric.py',
        'test_database_centric.py'
    ]
    
    all_present = True
    for file_path in required_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({file_size} bytes)")
        else:
            print(f"  ❌ {file_path} MISSING")
            all_present = False
    
    return all_present

def main():
    """Main verification function"""
    print("🔍 DATABASE-CENTRIC ARCHITECTURE VERIFICATION")
    print("=" * 60)
    print(f"Verification started at: {datetime.now()}")
    
    # Run all verifications
    schema_ok = verify_database_schema()
    migration_ok, total, blob, hash_count, cover = verify_data_migration()
    vector_removed = verify_vector_database_removal()
    files_ok = verify_implementation_files()
    
    # Final summary
    print("\n📊 VERIFICATION SUMMARY")
    print("=" * 40)
    print(f"Database schema: {'✅ PASS' if schema_ok else '❌ FAIL'}")
    print(f"Data migration: {'✅ PASS' if migration_ok else '❌ FAIL'}")
    print(f"Vector DB removal: {'✅ PASS' if vector_removed else '❌ FAIL'}")
    print(f"Implementation files: {'✅ PASS' if files_ok else '❌ FAIL'}")
    
    if migration_ok:
        print(f"\nMigration statistics:")
        print(f"  - {total} total records")
        print(f"  - {blob} records with PDF content")
        print(f"  - {hash_count} records with file hashes")
        print(f"  - {cover} records with cover images")
    
    overall_success = schema_ok and migration_ok and vector_removed and files_ok
    
    if overall_success:
        print("\n🎉 VERIFICATION SUCCESSFUL!")
        print("=" * 40)
        print("The database-centric architecture implementation is complete and working.")
        print("\nKey achievements:")
        print("  ✅ Single source of truth (database only)")
        print("  ✅ All PDF files stored as BLOBs")
        print("  ✅ Hash-based duplicate detection")
        print("  ✅ Cover images extracted and stored")
        print("  ✅ Vector database completely removed")
        print("  ✅ Simplified architecture with no filesystem dependencies")
        print("\nThe ERDB system is ready for production with the new architecture!")
    else:
        print("\n❌ VERIFICATION FAILED!")
        print("Some components of the database-centric architecture are not working correctly.")
        print("Please review the issues above and fix them before proceeding.")

if __name__ == '__main__':
    main()
