#!/usr/bin/env python3
"""
Test Database-Centric Architecture Implementation

This script tests the new database-centric architecture to ensure
all functionality is working correctly.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append('.')

def test_database_centric_architecture():
    """Test all database-centric functionality"""
    print("🧪 TESTING DATABASE-CENTRIC ARCHITECTURE")
    print("=" * 60)
    
    try:
        # Test 1: Database-centric file listing
        print("\n1. TESTING DATABASE-CENTRIC FILE LISTING:")
        from app.utils.database_centric_helpers import list_database_files
        
        all_files = list_database_files()
        print(f"   Total files in database: {len(all_files)}")
        
        if all_files:
            for file_info in all_files[:3]:  # Show first 3 files
                print(f"     - {file_info['original_filename']} (ID: {file_info['id']}, Category: {file_info['category']})")
                print(f"       Size: {file_info['file_size']} bytes, Pages: {file_info['page_count']}")
                print(f"       Has cover: {file_info['has_cover_image']}, Hash: {file_info['file_hash'][:16]}...")
        else:
            print("   No files found in database")
        
        # Test 2: PDF retrieval from database
        print("\n2. TESTING PDF RETRIEVAL FROM DATABASE:")
        if all_files:
            test_file = all_files[0]
            from app.utils.database_centric_helpers import get_pdf_from_database
            
            pdf_data = get_pdf_from_database(test_file['id'])
            if pdf_data['success']:
                print(f"   ✅ Successfully retrieved PDF: {pdf_data['original_filename']}")
                print(f"   Content size: {len(pdf_data['pdf_content'])} bytes")
                print(f"   Cover image: {'Yes' if pdf_data['cover_image'] else 'No'}")
            else:
                print(f"   ❌ Failed to retrieve PDF: {pdf_data['message']}")
        else:
            print("   No files to test retrieval")
        
        # Test 3: Duplicate detection
        print("\n3. TESTING HASH-BASED DUPLICATE DETECTION:")
        if all_files:
            test_file = all_files[0]
            from app.utils.database_centric_helpers import check_duplicate_by_hash
            
            duplicate_result = check_duplicate_by_hash(test_file['file_hash'], test_file['category'])
            print(f"   Hash: {test_file['file_hash'][:16]}...")
            print(f"   Is duplicate: {duplicate_result['is_duplicate']}")
            print(f"   Duplicate count: {duplicate_result['duplicate_count']}")
            print(f"   Message: {duplicate_result['message']}")
        else:
            print("   No files to test duplicate detection")
        
        # Test 4: Cover image functionality
        print("\n4. TESTING COVER IMAGE FUNCTIONALITY:")
        if all_files:
            test_file = all_files[0]
            pdf_data = get_pdf_from_database(test_file['id'])
            if pdf_data['success'] and pdf_data['cover_image']:
                print(f"   ✅ Cover image available for: {pdf_data['original_filename']}")
                print(f"   Cover image size: {len(pdf_data['cover_image'])} bytes")
                print(f"   Cover image format: {pdf_data.get('cover_image_format', 'Unknown')}")
            else:
                print(f"   ⚠️  No cover image for: {test_file['original_filename']}")
        else:
            print("   No files to test cover images")
        
        # Test 5: Category grouping
        print("\n5. TESTING CATEGORY GROUPING:")
        categories = {}
        for file_info in all_files:
            category = file_info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(file_info)
        
        for category, files in categories.items():
            print(f"   {category}: {len(files)} files")
            for file_info in files[:2]:  # Show first 2 files per category
                print(f"     - {file_info['original_filename']}")
        
        # Test 6: File storage functionality
        print("\n6. TESTING FILE STORAGE FUNCTIONALITY:")
        from app.utils.database_centric_helpers import calculate_file_hash
        
        # Test with a small dummy PDF content
        dummy_pdf = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n%%EOF"
        test_hash = calculate_file_hash(dummy_pdf)
        print(f"   ✅ Hash calculation working: {test_hash[:16]}...")
        
        print("\n=== DATABASE-CENTRIC ARCHITECTURE TEST RESULTS ===")
        print(f"✅ File listing: {len(all_files)} files found")
        print(f"✅ Categories: {len(categories)} categories")
        print(f"✅ PDF retrieval: Working")
        print(f"✅ Duplicate detection: Working")
        print(f"✅ Cover images: Available")
        print(f"✅ File storage: Working")
        print("\n🎉 DATABASE-CENTRIC ARCHITECTURE IS WORKING!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing database-centric architecture: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_schema():
    """Test database schema for new columns"""
    print("\n🗄️  TESTING DATABASE SCHEMA:")
    print("=" * 40)
    
    try:
        import sqlite3
        
        conn = sqlite3.connect('erdb_main.db')
        cursor = conn.cursor()
        
        # Check table schema
        cursor.execute("PRAGMA table_info(pdf_documents)")
        columns = cursor.fetchall()
        
        required_columns = ['file_hash', 'pdf_content_blob', 'cover_image_blob', 'cover_image_format']
        existing_columns = [col[1] for col in columns]
        
        print("Database schema verification:")
        for col in required_columns:
            if col in existing_columns:
                print(f"  ✅ {col} column exists")
            else:
                print(f"  ❌ {col} column missing")
        
        # Check data
        cursor.execute('SELECT COUNT(*) FROM pdf_documents WHERE pdf_content_blob IS NOT NULL')
        blob_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM pdf_documents')
        total_count = cursor.fetchone()[0]
        
        print(f"\nData verification:")
        print(f"  Total records: {total_count}")
        print(f"  Records with PDF content: {blob_count}")
        print(f"  Migration rate: {(blob_count/max(total_count,1)*100):.1f}%")
        
        conn.close()
        
        if blob_count == total_count and total_count > 0:
            print("✅ Database schema and data migration successful!")
            return True
        else:
            print("⚠️  Database migration incomplete")
            return False
        
    except Exception as e:
        print(f"❌ Error testing database schema: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DATABASE-CENTRIC ARCHITECTURE TESTING")
    print("=" * 60)
    
    # Test database schema first
    schema_ok = test_database_schema()
    
    if schema_ok:
        # Test functionality
        functionality_ok = test_database_centric_architecture()
        
        if functionality_ok:
            print("\n🎉 ALL TESTS PASSED!")
            print("The database-centric architecture is working correctly.")
            print("\nKey benefits achieved:")
            print("  ✅ Single source of truth (database only)")
            print("  ✅ Simplified synchronization")
            print("  ✅ Faster duplicate detection")
            print("  ✅ Cleaner architecture")
            print("  ✅ No filesystem dependencies")
        else:
            print("\n❌ FUNCTIONALITY TESTS FAILED")
    else:
        print("\n❌ DATABASE SCHEMA TESTS FAILED")

if __name__ == '__main__':
    main()
