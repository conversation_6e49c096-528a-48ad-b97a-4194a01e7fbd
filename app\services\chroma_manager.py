import os
import threading
import logging
from typing import Optional, Dict, Any
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
from chromadb.config import Settings
import json
import requests

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
COLLECTION_NAME = "unified_collection"

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]


class ChromaDBManager:
    """
    Singleton ChromaDB manager that ensures only one instance exists for the unified database.
    
    This manager handles:
    - Single ChromaDB instance creation and management
    - Consistent settings across all usage
    - Thread-safe operations
    - Embedding model management with fallbacks
    - Error handling and recovery
    """
    
    _instance: Optional['ChromaDBManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> 'ChromaDBManager':
        """Ensure singleton pattern with thread safety."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the ChromaDB manager (called only once due to singleton)."""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self._db_instance: Optional[Chroma] = None
        self._embedding_function: Optional[OllamaEmbeddings] = None
        self._db_lock = threading.Lock()
        self._embedding_lock = threading.Lock()
        self._current_model: Optional[str] = None
        
        # Ensure directory exists
        os.makedirs(CHROMA_PATH, exist_ok=True)
        logger.info("ChromaDBManager initialized")
    
    def _check_embedding_model_availability(self, model_name: str) -> bool:
        """Check if an embedding model is available in Ollama."""
        try:
            response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                available_models = [model["name"] for model in models]
                return model_name in available_models
            return False
        except Exception as e:
            logger.warning(f"Could not check model availability for {model_name}: {e}")
            return False
    
    def _get_embedding_prompts(self) -> tuple[str, str]:
        """Get embedding prompts from configuration."""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../../config/default_models.json')
            with open(config_path, 'r') as f:
                config = json.load(f)
            params = config.get('embedding_parameters', {})
            return params.get('embedding_prompt', ''), params.get('query_prompt', '')
        except Exception as e:
            logger.warning(f"Could not load embedding prompts from config: {e}")
            return '', ''
    
    def _get_embedding_function(self, model_name: Optional[str] = None) -> OllamaEmbeddings:
        """Get or create embedding function with fallback support."""
        target_model = model_name or TEXT_EMBEDDING_MODEL
        
        with self._embedding_lock:
            # Return cached embedding function if it matches the requested model
            if (self._embedding_function is not None and 
                self._current_model == target_model):
                return self._embedding_function
            
            # Try to initialize with the target model
            if not self._check_embedding_model_availability(target_model):
                logger.warning(f"Target model {target_model} not available, trying fallbacks")
                
                # Try fallback models
                for fallback_model in FALLBACK_EMBEDDING_MODELS:
                    if (fallback_model != target_model and 
                        self._check_embedding_model_availability(fallback_model)):
                        logger.info(f"Using fallback embedding model: {fallback_model}")
                        target_model = fallback_model
                        break
                else:
                    # No fallback available, try the original model anyway
                    logger.warning("No fallback models available, attempting original model")
            
            try:
                logger.info(f"Initializing embedding function with model: {target_model}")
                embedding_function = OllamaEmbeddings(
                    model=target_model,
                    base_url=OLLAMA_BASE_URL
                )
                
                # Cache the embedding function
                self._embedding_function = embedding_function
                self._current_model = target_model
                
                logger.info(f"Successfully initialized embedding function with model: {target_model}")
                return embedding_function
                
            except Exception as e:
                logger.error(f"Failed to initialize embedding function with model {target_model}: {e}")
                
                # Last resort fallback
                if target_model != "nomic-embed-text:latest":
                    try:
                        logger.info("Attempting last resort fallback to nomic-embed-text:latest")
                        embedding_function = OllamaEmbeddings(
                            model="nomic-embed-text:latest",
                            base_url=OLLAMA_BASE_URL
                        )
                        self._embedding_function = embedding_function
                        self._current_model = "nomic-embed-text:latest"
                        return embedding_function
                    except Exception as fallback_error:
                        logger.error(f"Last resort fallback failed: {fallback_error}")
                
                raise ValueError(f"Could not initialize any embedding model. Last error: {e}")
    
    def get_chroma_instance(self, model_name: Optional[str] = None) -> Chroma:
        """
        Get the singleton ChromaDB instance.
        
        Args:
            model_name: Optional specific embedding model to use
            
        Returns:
            ChromaDB instance
        """
        with self._db_lock:
            # If instance exists and model hasn't changed, return it
            target_model = model_name or TEXT_EMBEDDING_MODEL
            if (self._db_instance is not None and 
                self._current_model == target_model):
                logger.debug("Returning cached ChromaDB instance")
                return self._db_instance
            
            # If model changed, we need to recreate the instance
            if (self._db_instance is not None and 
                self._current_model != target_model):
                logger.info(f"Model changed from {self._current_model} to {target_model}, recreating instance")
                self._db_instance = None
            
            try:
                logger.info(f"Creating ChromaDB instance at {CHROMA_PATH}")
                
                # Get embedding function
                embedding_function = self._get_embedding_function(model_name)
                
                # Create ChromaDB instance with consistent settings
                self._db_instance = Chroma(
                    collection_name=COLLECTION_NAME,
                    persist_directory=CHROMA_PATH,
                    embedding_function=embedding_function,
                    client_settings=Settings(
                        anonymized_telemetry=False,
                        allow_reset=True
                    )
                )
                
                logger.info(f"Successfully created ChromaDB instance with model: {self._current_model}")
                return self._db_instance
                
            except Exception as e:
                logger.error(f"Failed to create ChromaDB instance: {e}")
                
                # Handle "instance already exists" error
                if "already exists" in str(e).lower():
                    logger.warning("ChromaDB instance already exists, attempting to connect to existing instance")
                    try:
                        # Try to connect to existing instance
                        embedding_function = self._get_embedding_function(model_name)
                        self._db_instance = Chroma(
                            collection_name=COLLECTION_NAME,
                            persist_directory=CHROMA_PATH,
                            embedding_function=embedding_function,
                            client_settings=Settings(
                                anonymized_telemetry=False,
                                allow_reset=True
                            )
                        )
                        logger.info("Successfully connected to existing ChromaDB instance")
                        return self._db_instance
                    except Exception as connect_error:
                        logger.error(f"Failed to connect to existing instance: {connect_error}")
                
                raise
    
    def reset_instance(self):
        """Reset the ChromaDB instance (for testing or recovery)."""
        with self._db_lock:
            logger.info("Resetting ChromaDB instance")
            self._db_instance = None
            self._embedding_function = None
            self._current_model = None
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the current collection."""
        try:
            db = self.get_chroma_instance()
            collection = db._collection
            return {
                "name": collection.name,
                "count": collection.count(),
                "model": self._current_model,
                "persist_directory": CHROMA_PATH
            }
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {"error": str(e)}


# Global singleton instance
_chroma_manager: Optional[ChromaDBManager] = None


def get_chroma_manager() -> ChromaDBManager:
    """
    Get the global ChromaDB manager instance.
    
    Returns:
        ChromaDBManager singleton instance
    """
    global _chroma_manager
    if _chroma_manager is None:
        _chroma_manager = ChromaDBManager()
    return _chroma_manager


def get_unified_chroma_db(model_name: Optional[str] = None) -> Chroma:
    """
    Get the unified ChromaDB instance.
    
    Args:
        model_name: Optional specific embedding model to use
        
    Returns:
        ChromaDB instance
    """
    manager = get_chroma_manager()
    return manager.get_chroma_instance(model_name)


def reset_chroma_instance():
    """Reset the ChromaDB instance (for testing or recovery)."""
    manager = get_chroma_manager()
    manager.reset_instance()
